<?php
// Test database connection
// Upload this to your public_html directory and visit it in browser

// Update these with your actual credentials
$host = 'localhost';
$dbname = 'u564231725_p2network';  // Update with your actual database name
$username = 'u564231725_p2network'; // Update with your actual username
$password = 'YOUR_ACTUAL_PASSWORD'; // Update with your actual password

echo "<h2>Database Connection Test</h2>";

try {
    $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);

    echo "✅ <strong>Database connection successful!</strong><br><br>";

    // Test if tables exist
    $tables = ['site_content', 'navigation', 'board_members', 'events'];

    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $result = $stmt->fetch();
            echo "✅ Table '$table': {$result['count']} records<br>";
        } catch (Exception $e) {
            echo "❌ Table '$table': Error - " . $e->getMessage() . "<br>";
        }
    }

    echo "<br><h3>Sample Data Check:</h3>";

    // Check site content
    try {
        $stmt = $pdo->query("SELECT title, subtitle FROM site_content LIMIT 1");
        $content = $stmt->fetch();
        if ($content) {
            echo "✅ Site content found: " . htmlspecialchars($content['title']) . "<br>";
        } else {
            echo "❌ No site content found<br>";
        }
    } catch (Exception $e) {
        echo "❌ Site content error: " . $e->getMessage() . "<br>";
    }

    // Check board members
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM board_members WHERE is_active = 1");
        $result = $stmt->fetch();
        echo "✅ Active board members: {$result['count']}<br>";
    } catch (Exception $e) {
        echo "❌ Board members error: " . $e->getMessage() . "<br>";
    }

} catch (PDOException $e) {
    echo "❌ <strong>Database connection failed:</strong><br>";
    echo "Error: " . $e->getMessage() . "<br>";
    echo "<br><strong>Common fixes:</strong><br>";
    echo "1. Check database credentials in this file<br>";
    echo "2. Verify database name in Hostinger hPanel<br>";
    echo "3. Ensure database user has proper permissions<br>";
}

echo "<br><br><em>Delete this file after testing!</em>";
?>