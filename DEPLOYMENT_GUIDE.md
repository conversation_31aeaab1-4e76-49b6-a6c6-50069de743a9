# P2 Website Deployment Guide for <PERSON><PERSON>

This guide will help you deploy the P2 Website to Hostinger web hosting with MySQL database support.

## Prerequisites

- Hostinger web hosting account with PHP and MySQL support
- Access to Hostinger control panel (hPanel)
- FTP client or file manager access
- Basic knowledge of MySQL database management

## Overview

The website has been converted from an Astro SSR application with JSON data storage to a static website with PHP API backend and MySQL database. This makes it compatible with <PERSON><PERSON>'s shared hosting environment.

## Step 1: Database Setup

### 1.1 Create MySQL Database

1. Log into your Hostinger hPanel
2. Go to **Databases** → **MySQL Databases**
3. Click **Create Database**
4. Database name: `p2website` (or your preferred name)
5. Create a database user with full privileges
6. Note down the database credentials:
   - Database name
   - Username
   - Password
   - Host (usually `localhost`)

### 1.2 Import Database Schema

1. In hPanel, go to **Databases** → **phpMyAdmin**
2. Select your database
3. Go to **Import** tab
4. Upload and execute `database/schema.sql`

### 1.3 Migrate Data

1. Upload the `database/` folder to your hosting account
2. Update database credentials in `database/config.php`:
   ```php
   $host = 'localhost';  // Your database host
   $dbname = 'your_actual_database_name';  // Your database name
   $username = 'your_actual_username';  // Your database username
   $password = 'your_actual_password';  // Your database password
   ```
3. Run the migration script via browser or command line:
   ```
   https://your-domain.com/database/migrate_data.php
   ```

## Step 2: File Upload

### 2.1 Build Static Files

On your local machine:
```bash
npm run build
```

### 2.2 Upload Files

Upload the following to your Hostinger public_html directory:

**Required files/folders:**
- `dist/` (contents of the build output) → Upload contents to `public_html/`
- `api/` → Upload to `public_html/api/`
- `database/` → Upload to `public_html/database/`
- `data/` → Upload to `public_html/data/` (for initial migration only)

**File structure on server:**
```
public_html/
├── index.html
├── assets/
├── api/
│   ├── auth.php
│   ├── content.php
│   ├── members.php
│   ├── education.php
│   ├── research.php
│   ├── gallery.php
│   └── projects.php
├── database/
│   ├── config.php
│   ├── schema.sql
│   └── migrate_data.php
└── data/ (temporary, for migration)
```

## Step 3: Configuration

### 3.1 Update API Base URL

Edit the API utility file if needed. The current configuration should work automatically:
- Production: Uses your domain automatically
- Development: Uses localhost

### 3.2 Set File Permissions

Ensure proper permissions:
- PHP files: 644
- Directories: 755
- Database config: 600 (if possible)

## Step 4: Security Setup

### 4.1 Change Default Admin Credentials

**IMPORTANT:** The migration script creates a default admin user:
- Username: `admin`
- Password: `admin123`

**Change these immediately after deployment:**

1. Go to your website's admin panel
2. Login with default credentials
3. Change the password in the database or create a new admin user

### 4.2 Secure Database Directory

Add `.htaccess` file to `database/` directory:
```apache
Order Deny,Allow
Deny from all
Allow from 127.0.0.1
```

### 4.3 Remove Migration Files

After successful migration:
1. Delete the `data/` directory
2. Delete `database/migrate_data.php`
3. Delete `test_php.php`

## Step 5: Testing

### 5.1 Test Website

1. Visit your domain
2. Check all pages load correctly:
   - Home page
   - Executive Board
   - Education
   - Research
   - Global Health
   - Photo Gallery
   - Admin Resources

### 5.2 Test Admin Panel

1. Go to `/admin`
2. Login with admin credentials
3. Test content editing functionality
4. Verify data saves correctly

### 5.3 Test API Endpoints

Test each API endpoint:
- `https://your-domain.com/api/content.php`
- `https://your-domain.com/api/members.php`
- `https://your-domain.com/api/education.php`
- `https://your-domain.com/api/research.php`
- `https://your-domain.com/api/gallery.php`
- `https://your-domain.com/api/projects.php`

## Step 6: Maintenance

### 6.1 Regular Backups

- Backup your MySQL database regularly
- Backup uploaded files and images
- Keep a copy of your database configuration

### 6.2 Updates

To update content:
1. Use the admin panel for content management
2. For code updates, rebuild locally and re-upload static files

### 6.3 Monitoring

- Monitor error logs in hPanel
- Check database performance
- Monitor disk space usage

## Troubleshooting

### Common Issues

**Database Connection Errors:**
- Verify database credentials in `config.php`
- Check database server status in hPanel
- Ensure database user has proper privileges

**API Errors:**
- Check PHP error logs
- Verify file permissions
- Ensure all required PHP extensions are enabled

**Missing Data:**
- Re-run the migration script
- Check database tables were created correctly
- Verify JSON data files are accessible during migration

**Admin Panel Issues:**
- Clear browser cache and cookies
- Check authentication cookies are being set
- Verify admin user exists in database

### Support

For additional support:
- Check Hostinger documentation
- Contact Hostinger support for hosting-specific issues
- Review PHP error logs for debugging

## Security Checklist

- [ ] Changed default admin credentials
- [ ] Secured database directory with .htaccess
- [ ] Removed migration files after deployment
- [ ] Set proper file permissions
- [ ] Enabled HTTPS (if available)
- [ ] Regular database backups scheduled

## Performance Optimization

- Enable gzip compression in .htaccess
- Optimize images before upload
- Use Hostinger's caching features
- Monitor database query performance

---

**Deployment completed successfully!** Your P2 Website is now ready for production use on Hostinger.