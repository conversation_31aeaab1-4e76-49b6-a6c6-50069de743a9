<?php
/**
 * Database Configuration for P2 Website
 * Configure these settings for your Hostinger MySQL database
 */

// Database configuration - UPDATE THESE VALUES FOR YOUR HOSTINGER SETUP
$host = 'localhost';  // Usually 'localhost' for Hostinger
$dbname = 'p2website';  // Your database name from Hostinger control panel
$username = 'your_db_username';  // Your database username from Hostinger
$password = 'your_db_password';  // Your database password from Hostinger

// PDO connection string
$dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";

// PDO options for better error handling and security
$options = [
    PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES   => false,
];

// Create PDO connection function
function getDbConnection() {
    global $dsn, $username, $password, $options;

    try {
        return new PDO($dsn, $username, $password, $options);
    } catch (PDOException $e) {
        // Log error and show user-friendly message
        error_log("Database connection failed: " . $e->getMessage());
        throw new Exception("Database connection failed. Please try again later.");
    }
}

// Helper function to execute queries safely
function executeQuery($pdo, $sql, $params = []) {
    try {
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    } catch (PDOException $e) {
        error_log("Query execution failed: " . $e->getMessage());
        throw new Exception("Database query failed. Please try again later.");
    }
}

// Helper function to get single record
function getSingleRecord($pdo, $sql, $params = []) {
    $stmt = executeQuery($pdo, $sql, $params);
    return $stmt->fetch();
}

// Helper function to get multiple records
function getMultipleRecords($pdo, $sql, $params = []) {
    $stmt = executeQuery($pdo, $sql, $params);
    return $stmt->fetchAll();
}

// Helper function to insert record and return ID
function insertRecord($pdo, $sql, $params = []) {
    executeQuery($pdo, $sql, $params);
    return $pdo->lastInsertId();
}

// Helper function to update record and return affected rows
function updateRecord($pdo, $sql, $params = []) {
    $stmt = executeQuery($pdo, $sql, $params);
    return $stmt->rowCount();
}

// Helper function to delete record and return affected rows
function deleteRecord($pdo, $sql, $params = []) {
    $stmt = executeQuery($pdo, $sql, $params);
    return $stmt->rowCount();
}
?>