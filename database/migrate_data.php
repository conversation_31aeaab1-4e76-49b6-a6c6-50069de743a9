<?php
/**
 * Data Migration Script for P2 Website
 * Migrates JSON data to MySQL database for Hostinger deployment
 */

require_once 'config.php';

class DataMigrator {
    private $pdo;

    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    public function migrate() {
        echo "Starting data migration...\n";

        try {
            $this->migrateSiteContent();
            $this->migrateNavigation();
            $this->migrateBoardMembers();
            $this->migrateFellowshipPrograms();
            $this->migrateTrainingResources();
            $this->migrateCertificationLevels();
            $this->migrateWebinars();
            $this->migrateConferences();
            $this->migrateLandmarkPapers();
            $this->migrateResearchProjects();
            $this->migrateResearchOpportunities();
            $this->migrateGlobalHealthProjects();
            $this->migrateGallery();
            $this->migrateEvents();
            $this->migrateAdminResources();
            $this->createDefaultAdminUser();

            echo "Data migration completed successfully!\n";
        } catch (Exception $e) {
            echo "Migration failed: " . $e->getMessage() . "\n";
            throw $e;
        }
    }

    private function migrateSiteContent() {
        echo "Migrating site content...\n";

        $contentData = json_decode(file_get_contents('../data/content.json'), true);
        $site = $contentData['site'];

        $stmt = $this->pdo->prepare("
            INSERT INTO site_content (title, subtitle, description, hero_title, hero_subtitle, hero_highlights)
            VALUES (?, ?, ?, ?, ?, ?)
        ");

        $stmt->execute([
            $site['title'],
            $site['subtitle'],
            $site['description'],
            $site['hero']['title'],
            $site['hero']['subtitle'],
            json_encode($site['hero']['highlights'])
        ]);
    }

    private function migrateNavigation() {
        echo "Migrating navigation...\n";

        $contentData = json_decode(file_get_contents('../data/content.json'), true);
        $navigation = $contentData['navigation'];

        $stmt = $this->pdo->prepare("
            INSERT INTO navigation (name, href, sort_order)
            VALUES (?, ?, ?)
        ");

        foreach ($navigation as $index => $item) {
            $stmt->execute([
                $item['name'],
                $item['href'],
                $index
            ]);
        }
    }

    private function migrateBoardMembers() {
        echo "Migrating board members...\n";

        $membersData = json_decode(file_get_contents('../data/members.json'), true);
        $members = $membersData['boardMembers'];

        $stmt = $this->pdo->prepare("
            INSERT INTO board_members (id, name, position, specialty, institution, bio, email, image, linkedin, sort_order)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        foreach ($members as $index => $member) {
            $stmt->execute([
                $member['id'],
                $member['name'],
                $member['position'],
                $member['specialty'] ?? null,
                $member['institution'] ?? null,
                $member['bio'] ?? null,
                $member['email'] ?? null,
                $member['image'] ?? null,
                $member['linkedin'] ?? null,
                $index
            ]);
        }
    }

    private function migrateFellowshipPrograms() {
        echo "Migrating fellowship programs...\n";

        $educationData = json_decode(file_get_contents('../data/education.json'), true);
        $programs = $educationData['fellowshipPrograms'];

        $stmt = $this->pdo->prepare("
            INSERT INTO fellowship_programs (id, title, duration, location, description, curriculum, requirements,
                                           application_deadline, start_date, positions, stipend)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        foreach ($programs as $program) {
            $stmt->execute([
                $program['id'],
                $program['title'],
                $program['duration'] ?? null,
                $program['location'] ?? null,
                $program['description'] ?? null,
                json_encode($program['curriculum'] ?? []),
                json_encode($program['requirements'] ?? []),
                $program['applicationDeadline'] ?? null,
                $program['startDate'] ?? null,
                $program['positions'] ?? null,
                $program['stipend'] ?? null
            ]);
        }
    }

    private function migrateTrainingResources() {
        echo "Migrating training resources...\n";

        $educationData = json_decode(file_get_contents('../data/education.json'), true);
        $resources = $educationData['trainingResources'];

        $stmt = $this->pdo->prepare("
            INSERT INTO training_resources (title, type, duration, cost, description, modules, locations, features)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");

        foreach ($resources as $resource) {
            $stmt->execute([
                $resource['title'],
                $resource['type'],
                $resource['duration'] ?? null,
                $resource['cost'] ?? null,
                $resource['description'] ?? null,
                json_encode($resource['modules'] ?? []),
                json_encode($resource['locations'] ?? []),
                json_encode($resource['features'] ?? [])
            ]);
        }
    }

    private function migrateCertificationLevels() {
        echo "Migrating certification levels...\n";

        $educationData = json_decode(file_get_contents('../data/education.json'), true);
        $levels = $educationData['certificationInfo']['levels'];

        $stmt = $this->pdo->prepare("
            INSERT INTO certification_levels (name, requirements, validity_period, cost, sort_order)
            VALUES (?, ?, ?, ?, ?)
        ");

        foreach ($levels as $index => $level) {
            $stmt->execute([
                $level['name'],
                json_encode($level['requirements']),
                $level['validityPeriod'],
                $level['cost'],
                $index
            ]);
        }
    }

    private function migrateWebinars() {
        echo "Migrating webinars...\n";

        $educationData = json_decode(file_get_contents('../data/education.json'), true);
        $webinars = $educationData['learningResources']['pastWebinars'];

        $stmt = $this->pdo->prepare("
            INSERT INTO webinars (id, title, date, duration, presenter, description, video_url, slides_url)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");

        foreach ($webinars as $webinar) {
            $stmt->execute([
                $webinar['id'],
                $webinar['title'],
                $webinar['date'],
                $webinar['duration'] ?? null,
                $webinar['presenter'] ?? null,
                $webinar['description'] ?? null,
                $webinar['videoUrl'] ?? null,
                $webinar['slides'] ?? null
            ]);
        }
    }

    private function migrateConferences() {
        echo "Migrating conferences...\n";

        $educationData = json_decode(file_get_contents('../data/education.json'), true);
        $conferences = $educationData['learningResources']['pastConferences'];

        $stmt = $this->pdo->prepare("
            INSERT INTO conferences (id, title, date, location, description, highlights, materials_url)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");

        foreach ($conferences as $conference) {
            $stmt->execute([
                $conference['id'],
                $conference['title'],
                $conference['date'],
                $conference['location'] ?? null,
                $conference['description'] ?? null,
                json_encode($conference['highlights'] ?? []),
                $conference['materials'] ?? null
            ]);
        }
    }

    private function migrateLandmarkPapers() {
        echo "Migrating landmark papers...\n";

        $educationData = json_decode(file_get_contents('../data/education.json'), true);
        $papers = $educationData['learningResources']['landmarkPapers'];

        $stmt = $this->pdo->prepare("
            INSERT INTO landmark_papers (id, title, authors, journal, year, doi, abstract, url)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");

        foreach ($papers as $paper) {
            $stmt->execute([
                $paper['id'],
                $paper['title'],
                $paper['authors'] ?? null,
                $paper['journal'] ?? null,
                $paper['year'] ?? null,
                $paper['doi'] ?? null,
                $paper['abstract'] ?? null,
                $paper['url'] ?? null
            ]);
        }
    }

    private function migrateResearchProjects() {
        echo "Migrating research projects...\n";

        $researchData = json_decode(file_get_contents('../data/research.json'), true);
        $projects = $researchData['currentProjects'];

        $stmt = $this->pdo->prepare("
            INSERT INTO research_projects (id, title, principal_investigator, status, start_date, end_date,
                                         funding, funding_source, description, objectives, current_phase,
                                         participants, publications)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        foreach ($projects as $project) {
            $stmt->execute([
                $project['id'],
                $project['title'],
                $project['principalInvestigator'] ?? null,
                $project['status'] ?? null,
                $project['startDate'] ?? null,
                $project['endDate'] ?? null,
                $project['funding'] ?? null,
                $project['fundingSource'] ?? null,
                $project['description'] ?? null,
                json_encode($project['objectives'] ?? []),
                $project['currentPhase'] ?? null,
                $project['participants'] ?? 0,
                $project['publications'] ?? 0
            ]);
        }
    }

    private function migrateResearchOpportunities() {
        echo "Migrating research opportunities...\n";

        $researchData = json_decode(file_get_contents('../data/research.json'), true);
        $opportunities = $researchData['howToGetInvolved']['opportunities'];

        $stmt = $this->pdo->prepare("
            INSERT INTO research_opportunities (type, description, requirements, contact, sort_order)
            VALUES (?, ?, ?, ?, ?)
        ");

        foreach ($opportunities as $index => $opportunity) {
            $stmt->execute([
                $opportunity['type'],
                $opportunity['description'] ?? null,
                json_encode($opportunity['requirements'] ?? []),
                $opportunity['contact'] ?? null,
                $index
            ]);
        }
    }

    private function migrateGlobalHealthProjects() {
        echo "Migrating global health projects...\n";

        $projectsData = json_decode(file_get_contents('../data/projects.json'), true);
        $projects = $projectsData['globalHealthProjects'];

        $stmt = $this->pdo->prepare("
            INSERT INTO global_health_projects (id, title, location, status, lead_investigator, start_date,
                                              end_date, description, participants, budget, outcomes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        foreach ($projects as $project) {
            $stmt->execute([
                $project['id'],
                $project['title'],
                $project['location'] ?? null,
                $project['status'] ?? null,
                $project['leadInvestigator'] ?? null,
                $project['startDate'] ?? null,
                $project['endDate'] ?? null,
                $project['description'] ?? null,
                $project['participants'] ?? 0,
                $project['budget'] ?? null,
                $project['outcomes'] ?? null
            ]);
        }
    }

    private function migrateGallery() {
        echo "Migrating gallery...\n";

        $galleryData = json_decode(file_get_contents('../data/gallery.json'), true);
        $sections = $galleryData['sections'];

        $sectionStmt = $this->pdo->prepare("
            INSERT INTO gallery_sections (id, title, year, description, sort_order)
            VALUES (?, ?, ?, ?, ?)
        ");

        $photoStmt = $this->pdo->prepare("
            INSERT INTO gallery_photos (section_id, url, caption, alt_text, sort_order)
            VALUES (?, ?, ?, ?, ?)
        ");

        foreach ($sections as $index => $section) {
            $sectionStmt->execute([
                $section['id'],
                $section['title'],
                $section['year'] ?? null,
                $section['description'] ?? null,
                $index
            ]);

            if (isset($section['photos'])) {
                foreach ($section['photos'] as $photoIndex => $photo) {
                    $photoStmt->execute([
                        $section['id'],
                        $photo['url'],
                        $photo['caption'] ?? null,
                        $photo['alt'] ?? null,
                        $photoIndex
                    ]);
                }
            }
        }
    }

    private function migrateEvents() {
        echo "Migrating events...\n";

        $contentData = json_decode(file_get_contents('../data/content.json'), true);
        $events = $contentData['site']['upcomingEvents'];

        $stmt = $this->pdo->prepare("
            INSERT INTO events (id, title, date, time, location, description, registration_link)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");

        foreach ($events as $event) {
            $stmt->execute([
                $event['id'],
                $event['title'],
                $event['date'],
                $event['time'] ?? null,
                $event['location'] ?? null,
                $event['description'] ?? null,
                $event['registrationLink'] ?? null
            ]);
        }
    }

    private function migrateAdminResources() {
        echo "Migrating admin resources...\n";

        $contentData = json_decode(file_get_contents('../data/content.json'), true);
        $resources = $contentData['adminResources']['links'];

        $stmt = $this->pdo->prepare("
            INSERT INTO admin_resources (title, description, url, category, sort_order)
            VALUES (?, ?, ?, ?, ?)
        ");

        foreach ($resources as $index => $resource) {
            $stmt->execute([
                $resource['title'],
                $resource['description'] ?? null,
                $resource['url'] ?? null,
                $resource['category'] ?? null,
                $index
            ]);
        }
    }

    private function createDefaultAdminUser() {
        echo "Creating default admin user...\n";

        $stmt = $this->pdo->prepare("
            INSERT INTO admin_users (username, password_hash, email)
            VALUES (?, ?, ?)
        ");

        // Default admin credentials - CHANGE THESE AFTER DEPLOYMENT!
        $stmt->execute([
            'admin',
            password_hash('admin123', PASSWORD_DEFAULT),
            '<EMAIL>'
        ]);

        echo "Default admin user created with username: admin, password: admin123\n";
        echo "IMPORTANT: Change these credentials after deployment!\n";
    }
}

// Run migration if called directly
if (basename(__FILE__) == basename($_SERVER["SCRIPT_FILENAME"])) {
    try {
        $pdo = new PDO($dsn, $username, $password, $options);
        $migrator = new DataMigrator($pdo);
        $migrator->migrate();
    } catch (Exception $e) {
        echo "Error: " . $e->getMessage() . "\n";
        exit(1);
    }
}
?>