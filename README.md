# Pediatric POCUS Society Website

A modern, professional website for a Pediatric POCUS (Point-of-Care Ultrasound) medical organization built with Astro framework, featuring glassmorphism design, file-based storage, and a built-in admin panel.

## ✨ Features

### 🎨 Design & User Experience
- **Modern Medical Aesthetic**: Professional healthcare design with stunning visual appeal
- **Glassmorphism Effects**: Translucent panels with backdrop blur effects
- **Purple-Blue Gradient Theme**: Beautiful color scheme (#667eea to #764ba2)
- **Smooth Animations**: Card hover effects, fade-ins, and micro-interactions
- **Fully Responsive**: Optimized for all devices and screen sizes
- **Contemporary Typography**: Bold, dynamic layouts with Inter font family

### 📄 Pages & Navigation
- **Home/Intro**: Hero section with organization highlights and upcoming events
- **Executive Board**: Interactive cards displaying board members with bios
- **Education**: Fellowship programs, training resources, and certification info
- **Research**: Current research projects and collaboration opportunities
- **Global Health**: Table/spreadsheet view of international projects
- **Admin Resources**: Links to policies, research papers, and statements

### 🔧 Admin Panel Features
- **Built-in Authentication**: Simple login system (username: 'admin', password: 'password')
- **Content Management**: Edit all text, images, and links directly within the website
- **Board Member Management**: Add, edit, and remove board members with CRUD operations
- **Project Management**: Manage global health projects with full CRUD functionality
- **Event Management**: Update upcoming events and meeting information
- **File-Based Storage**: All data automatically saved to JSON files

### 🏗️ Technical Architecture
- **Astro Framework**: Modern static site generator with React islands
- **React Components**: Interactive admin components for dynamic functionality
- **JSON File Storage**: Self-contained data management in `/data` directory
- **Internal API Endpoints**: Server-side endpoints for data operations
- **No External Dependencies**: Everything works within the website itself
- **Automatic Data Persistence**: Changes save to files immediately

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

1. **Clone and install dependencies:**
```bash
git clone <repository-url>
cd p2website
npm install
```

2. **Start the development server:**
```bash
npm run dev
```

3. **Open your browser:**
Navigate to `http://localhost:4321`

## 📁 Project Structure

```
p2website/
├── data/                          # JSON data files
│   ├── content.json              # Site content and navigation
│   ├── members.json              # Board members data
│   ├── projects.json             # Global health projects
│   ├── research.json             # Research projects
│   └── education.json            # Educational programs
├── src/
│   ├── components/               # Reusable components
│   │   ├── Layout.astro         # Main layout wrapper
│   │   ├── Navigation.astro     # Navigation bar
│   │   ├── Hero.astro           # Hero section
│   │   └── AdminPanel.tsx       # React admin interface
│   ├── pages/                   # Route pages
│   │   ├── index.astro          # Home page
│   │   ├── board.astro          # Executive board
│   │   ├── education.astro      # Education programs
│   │   ├── research.astro       # Research projects
│   │   ├── global-health.astro  # Global health initiatives
│   │   ├── admin.astro          # Admin resources
│   │   ├── admin-panel.astro    # Admin management panel
│   │   └── api/                 # API endpoints
│   │       ├── auth.ts          # Authentication
│   │       ├── content.ts       # Content management
│   │       ├── members.ts       # Members CRUD
│   │       └── projects.ts      # Projects CRUD
│   └── styles/
│       └── global.css           # Global styles with glassmorphism
├── public/                      # Static assets
├── astro.config.mjs            # Astro configuration
├── tailwind.config.mjs         # Tailwind CSS configuration
└── package.json                # Dependencies and scripts
```

## 🔐 Admin Panel Usage

### Accessing the Admin Panel

1. **Navigate to the admin panel:**
   - Go to `http://localhost:4321/admin-panel`
   - Or click the "Admin" button in the navigation

2. **Login with demo credentials:**
   - Username: `admin`
   - Password: `password`

### Managing Content

#### Board Members
- **Add New Member**: Click "Add New Member" button
- **Edit Member**: Click "Edit" on any member card
- **Delete Member**: Click "Delete" to remove a member
- **Fields**: Name, position, specialty, institution, bio, email

#### Global Health Projects
- **Add Project**: Click "Add New Project" button
- **Edit Project**: Click "Edit" on any project
- **Update Status**: Change between Active, Planning, Completed
- **Track Progress**: Update participants, budget, outcomes

#### Events Management
- **Add Event**: Create new upcoming events
- **Edit Details**: Update date, time, location, description
- **Registration Links**: Manage registration URLs

### Data Persistence
All changes are automatically saved to JSON files in the `/data` directory:
- Changes persist between server restarts
- No database required - everything is file-based
- Data can be backed up by copying the `/data` folder

## 🎨 Customization

### Styling
The website uses a glassmorphism design with customizable elements:

```css
/* Main gradient background */
.bg-gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Glass effect components */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}
```

### Color Scheme
Modify colors in `tailwind.config.mjs`:
```javascript
colors: {
  primary: {
    500: '#667eea', // Main primary color
    // ... other shades
  },
  secondary: {
    500: '#764ba2', // Main secondary color
    // ... other shades
  }
}
```

### Content Updates
Edit JSON files in the `/data` directory to update:
- Site content and navigation (`content.json`)
- Board member information (`members.json`)
- Research projects (`research.json`)
- Educational programs (`education.json`)
- Global health projects (`projects.json`)

## 🚀 Deployment

### Build for Production
```bash
npm run build
```

### Preview Production Build
```bash
npm run preview
```

### Deployment Options
The website can be deployed to any static hosting service:
- **Netlify**: Drag and drop the `dist` folder
- **Vercel**: Connect your Git repository
- **GitHub Pages**: Use GitHub Actions for automatic deployment
- **Traditional Hosting**: Upload the `dist` folder contents

## 🧞 Commands

| Command                   | Action                                           |
| :------------------------ | :----------------------------------------------- |
| `npm install`             | Installs dependencies                            |
| `npm run dev`             | Starts local dev server at `localhost:4321`     |
| `npm run build`           | Build your production site to `./dist/`         |
| `npm run preview`         | Preview your build locally, before deploying    |
| `npm run astro ...`       | Run CLI commands like `astro add`, `astro check`|
| `npm run astro -- --help` | Get help using the Astro CLI                    |

## 📝 Content Management

### JSON Data Structure

#### Board Members (`data/members.json`)
```json
{
  "boardMembers": [
    {
      "id": 1,
      "name": "Dr. Sarah Chen",
      "position": "President",
      "specialty": "Pediatric Emergency Medicine",
      "institution": "Children's Hospital of Philadelphia",
      "bio": "Dr. Chen is a leading expert...",
      "email": "<EMAIL>",
      "image": "/images/board/sarah-chen.jpg"
    }
  ]
}
```

#### Global Health Projects (`data/projects.json`)
```json
{
  "globalHealthProjects": [
    {
      "id": 1,
      "title": "POCUS Training Initiative - Kenya",
      "location": "Nairobi, Kenya",
      "status": "Active",
      "leadInvestigator": "Dr. David Kim",
      "startDate": "2023-01-15",
      "endDate": "2024-12-31",
      "description": "Establishing comprehensive POCUS training...",
      "participants": 45,
      "budget": "$125,000",
      "outcomes": "Trained 45 healthcare providers..."
    }
  ]
}
```

## 🔧 Technical Details

### Authentication System
- Simple hardcoded authentication for demo purposes
- Session management using HTTP-only cookies
- Token-based authentication for API endpoints
- Automatic logout after 24 hours

### API Endpoints
- `POST /api/auth` - Login authentication
- `DELETE /api/auth` - Logout
- `GET /api/auth` - Check authentication status
- `GET|PUT /api/content` - Content management
- `GET|POST|PUT|DELETE /api/members` - Board members CRUD
- `GET|POST|PUT|DELETE /api/projects` - Projects CRUD

### File Operations
All data operations use Node.js `fs` module:
- `readFileSync()` for reading JSON data
- `writeFileSync()` for saving changes
- Automatic JSON formatting with 2-space indentation
- Error handling for file operations

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Built with [Astro](https://astro.build/) framework
- Styled with [Tailwind CSS](https://tailwindcss.com/)
- Interactive components with [React](https://reactjs.org/)
- Typography using [Inter](https://fonts.google.com/specimen/Inter) font family

---

**Note**: This is a demonstration website with placeholder content. In a production environment, you would want to implement proper authentication, database storage, and security measures.
