@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gradient-primary min-h-screen;
    background-attachment: fixed;
  }
}

@layer components {
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }
  
  .glass-card {
    @apply glass rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300;
  }
  
  .glass-nav {
    @apply glass rounded-full px-6 py-3 shadow-lg;
  }
  
  .btn-primary {
    @apply bg-gradient-primary text-white px-6 py-3 rounded-full font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300;
  }
  
  .btn-secondary {
    @apply glass text-white px-6 py-3 rounded-full font-semibold hover:bg-white/20 transition-all duration-300;
  }
  
  .text-gradient {
    @apply bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent;
  }
  
  .hero-title {
    @apply text-5xl md:text-7xl font-bold text-white mb-6 animate-fade-in;
  }
  
  .hero-subtitle {
    @apply text-xl md:text-2xl text-white/90 mb-8 animate-slide-up;
  }
  
  .section-title {
    @apply text-3xl md:text-4xl font-bold text-white mb-8 text-center;
  }
  
  .card-hover {
    @apply transform hover:scale-105 hover:-translate-y-2 transition-all duration-300;
  }
  
  .floating {
    @apply animate-float;
  }
  
  .medical-gradient {
    background: linear-gradient(135deg, 
      rgba(102, 126, 234, 0.9) 0%, 
      rgba(118, 75, 162, 0.9) 50%, 
      rgba(168, 85, 247, 0.9) 100%);
  }
  
  .glass-table {
    @apply glass rounded-xl overflow-hidden;
  }
  
  .glass-table th {
    @apply bg-white/20 text-white font-semibold p-4 text-left;
  }
  
  .glass-table td {
    @apply p-4 text-white/90 border-b border-white/10;
  }
  
  .glass-table tr:hover {
    @apply bg-white/5;
  }
  
  .status-active {
    @apply bg-green-500/20 text-green-300 px-3 py-1 rounded-full text-sm font-medium;
  }
  
  .status-planning {
    @apply bg-yellow-500/20 text-yellow-300 px-3 py-1 rounded-full text-sm font-medium;
  }
  
  .status-completed {
    @apply bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full text-sm font-medium;
  }
  
  .admin-panel {
    @apply glass-card max-w-4xl mx-auto;
  }
  
  .form-input {
    @apply glass rounded-lg px-4 py-2 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-primary-400;
  }
  
  .form-textarea {
    @apply glass rounded-lg px-4 py-2 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-primary-400 min-h-[100px];
  }
  
  .form-label {
    @apply text-white font-medium mb-2 block;
  }
}

@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }
  
  .backdrop-blur-strong {
    backdrop-filter: blur(20px);
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Loading animation */
.loading {
  @apply animate-pulse;
}

/* Responsive text */
@media (max-width: 640px) {
  .hero-title {
    @apply text-4xl;
  }
  
  .hero-subtitle {
    @apply text-lg;
  }
  
  .section-title {
    @apply text-2xl;
  }
}
