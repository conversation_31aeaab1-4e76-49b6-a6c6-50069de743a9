---
import Navigation from './Navigation.astro';
import '../styles/global.css';

export interface Props {
  title: string;
  description?: string;
}

const { title, description = "Advancing Point-of-Care Ultrasound in Pediatric Medicine" } = Astro.props;
---

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="description" content={description} />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <title>{title}</title>
  </head>
  <body>
    <Navigation />
    <main class="min-h-screen">
      <slot />
    </main>
    <footer class="glass-card mx-4 mb-4 text-center">
      <div class="max-w-6xl mx-auto py-8">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
          <div>
            <h3 class="text-xl font-semibold text-white mb-4">Contact</h3>
            <p class="text-white/80"><EMAIL></p>
            <p class="text-white/80">+1 (555) 123-4567</p>
          </div>
          <div>
            <h3 class="text-xl font-semibold text-white mb-4">Quick Links</h3>
            <div class="space-y-2">
              <a href="/education" class="block text-white/80 hover:text-white transition-colors">Education</a>
              <a href="/research" class="block text-white/80 hover:text-white transition-colors">Research</a>
              <a href="/global-health" class="block text-white/80 hover:text-white transition-colors">Global Health</a>
            </div>
          </div>
          <div>
            <h3 class="text-xl font-semibold text-white mb-4">Follow Us</h3>
            <div class="flex justify-center space-x-4">
              <a href="#" class="text-white/80 hover:text-white transition-colors">Twitter</a>
              <a href="#" class="text-white/80 hover:text-white transition-colors">LinkedIn</a>
              <a href="#" class="text-white/80 hover:text-white transition-colors">YouTube</a>
            </div>
          </div>
        </div>
        <div class="border-t border-white/20 pt-8">
          <p class="text-white/60">© 2024 Pediatric POCUS Society. All rights reserved.</p>
        </div>
      </div>
    </footer>
  </body>
</html>

<style>
  body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
    min-height: 100vh;
  }
</style>
