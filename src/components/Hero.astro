---
import { readFileSync } from 'fs';
import { join } from 'path';

// Read content data
const contentPath = join(process.cwd(), 'data', 'content.json');
const contentData = JSON.parse(readFileSync(contentPath, 'utf-8'));
const { hero, upcomingEvents } = contentData.site;
---

<section class="relative min-h-screen flex items-center justify-center px-4 pt-32">
  <!-- Background Elements -->
  <div class="absolute inset-0 overflow-hidden">
    <div class="absolute top-1/4 left-1/4 w-64 h-64 bg-white/5 rounded-full blur-3xl floating"></div>
    <div class="absolute bottom-1/4 right-1/4 w-96 h-96 bg-primary-400/10 rounded-full blur-3xl floating" style="animation-delay: -3s;"></div>
    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-128 h-128 bg-secondary-400/5 rounded-full blur-3xl floating" style="animation-delay: -1.5s;"></div>
  </div>
  
  <div class="relative z-10 max-w-6xl mx-auto text-center">
    <!-- Main Hero Content -->
    <div class="mb-16">
      <h1 class="hero-title text-shadow">
        {hero.title}
      </h1>
      <p class="hero-subtitle text-shadow">
        {hero.subtitle}
      </p>
      
      <!-- Highlights -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
        {hero.highlights.map((highlight: string, index: number) => (
          <div 
            class="glass-card card-hover text-center"
            style={`animation-delay: ${index * 0.2}s`}
          >
            <div class="w-12 h-12 bg-gradient-secondary rounded-full mx-auto mb-4 flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <p class="text-white font-medium">{highlight}</p>
          </div>
        ))}
      </div>
      
      <!-- CTA Buttons -->
      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <a href="/education" class="btn-primary">
          Explore Programs
        </a>
        <a href="/research" class="btn-secondary">
          View Research
        </a>
      </div>
    </div>
    
    <!-- Upcoming Events Section -->
    <div class="glass-card max-w-4xl mx-auto">
      <h2 class="text-2xl font-bold text-white mb-6 text-center">Upcoming Events</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        {upcomingEvents.map((event: any) => (
          <div class="glass rounded-xl p-6 card-hover">
            <div class="flex items-start justify-between mb-4">
              <div>
                <h3 class="text-lg font-semibold text-white mb-2">{event.title}</h3>
                <div class="flex items-center text-white/80 text-sm mb-1">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                  {new Date(event.date).toLocaleDateString()} at {event.time}
                </div>
                <div class="flex items-center text-white/80 text-sm mb-3">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  </svg>
                  {event.location}
                </div>
              </div>
            </div>
            <p class="text-white/90 text-sm mb-4">{event.description}</p>
            <a 
              href={event.registrationLink} 
              class="inline-flex items-center text-primary-300 hover:text-primary-200 font-medium text-sm transition-colors"
            >
              Register Now
              <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
              </svg>
            </a>
          </div>
        ))}
      </div>
    </div>
  </div>
</section>
