---
import Layout from '../components/Layout.astro';
import { fetchGalleryData } from '../utils/api.js';

// Fetch gallery data from PHP API
const galleryData = await fetchGalleryData();
const { title, description, sections } = galleryData;
---

<Layout title="Photo Gallery - Pediatric POCUS Society">
  <section class="pt-32 pb-16 px-4">
    <div class="max-w-6xl mx-auto">
      <div class="text-center mb-16">
        <h1 class="section-title">{title}</h1>
        <p class="text-xl text-white/90 max-w-3xl mx-auto">
          {description}
        </p>
      </div>
      
      {sections.length === 0 ? (
        <div class="glass-card text-center">
          <h2 class="text-2xl font-bold text-white mb-4">Gallery Coming Soon</h2>
          <p class="text-white/90 mb-6">
            We're currently building our photo gallery. Check back soon for amazing photos from our events and activities!
          </p>
          <a href="/admin-panel" class="btn-primary">
            Admin: Add Photos
          </a>
        </div>
      ) : (
        <div class="space-y-16">
          {sections.map((section: any, sectionIndex: number) => (
            <div 
              class="glass-card"
              style={`animation: slideUp 0.6s ease-out ${sectionIndex * 0.2}s both`}
            >
              <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-white mb-2">{section.title}</h2>
                <div class="text-primary-300 font-semibold mb-4">{section.year}</div>
                <p class="text-white/90 max-w-2xl mx-auto">{section.description}</p>
              </div>
              
              {section.photos && section.photos.length > 0 && (
                <div class="relative">
                  <!-- Carousel Container -->
                  <div 
                    class="carousel-container overflow-hidden rounded-xl"
                    data-section-id={section.id}
                  >
                    <div class="carousel-track flex transition-transform duration-500 ease-in-out">
                      {section.photos.map((photo: any, photoIndex: number) => (
                        <div class="carousel-slide flex-shrink-0 w-full">
                          <div class="relative">
                            <img 
                              src={photo.url} 
                              alt={photo.alt}
                              class="w-full h-96 object-cover rounded-xl"
                              loading="lazy"
                            />
                            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6 rounded-b-xl">
                              <p class="text-white font-medium">{photo.caption}</p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <!-- Carousel Controls -->
                  {section.photos.length > 1 && (
                    <div class="flex items-center justify-between mt-6">
                      <button 
                        class="carousel-prev bg-white/10 hover:bg-white/20 text-white p-3 rounded-full transition-colors"
                        data-section-id={section.id}
                        aria-label="Previous photo"
                      >
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                      </button>
                      
                      <div class="flex space-x-2">
                        {section.photos.map((photo: any, photoIndex: number) => (
                          <button 
                            class={`carousel-dot w-3 h-3 rounded-full transition-colors ${photoIndex === 0 ? 'bg-white' : 'bg-white/40'}`}
                            data-section-id={section.id}
                            data-photo-index={photoIndex}
                            aria-label={`Go to photo ${photoIndex + 1}`}
                          ></button>
                        ))}
                      </div>
                      
                      <button 
                        class="carousel-next bg-white/10 hover:bg-white/20 text-white p-3 rounded-full transition-colors"
                        data-section-id={section.id}
                        aria-label="Next photo"
                      >
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
      
      <!-- Call to Action -->
      <div class="glass-card text-center mt-16">
        <h2 class="text-2xl font-bold text-white mb-4">Share Your Memories</h2>
        <p class="text-white/90 mb-6 max-w-2xl mx-auto">
          Have photos from our events or activities? We'd love to feature them in our gallery!
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a href="mailto:<EMAIL>" class="btn-primary">
            Submit Photos
          </a>
          <a href="/admin-panel" class="btn-secondary">
            Admin Panel
          </a>
        </div>
      </div>
    </div>
  </section>
</Layout>

<script>
  // Carousel functionality
  document.addEventListener('DOMContentLoaded', function() {
    const carousels = document.querySelectorAll('.carousel-container');
    
    carousels.forEach(carousel => {
      const sectionId = carousel.getAttribute('data-section-id');
      const track = carousel.querySelector('.carousel-track');
      const slides = carousel.querySelectorAll('.carousel-slide');
      const prevBtn = document.querySelector(`.carousel-prev[data-section-id="${sectionId}"]`);
      const nextBtn = document.querySelector(`.carousel-next[data-section-id="${sectionId}"]`);
      const dots = document.querySelectorAll(`.carousel-dot[data-section-id="${sectionId}"]`);
      
      let currentIndex = 0;
      
      function updateCarousel() {
        if (track) {
          track.style.transform = `translateX(-${currentIndex * 100}%)`;
        }
        
        // Update dots
        dots.forEach((dot, index) => {
          if (index === currentIndex) {
            dot.classList.remove('bg-white/40');
            dot.classList.add('bg-white');
          } else {
            dot.classList.remove('bg-white');
            dot.classList.add('bg-white/40');
          }
        });
      }
      
      // Previous button
      if (prevBtn) {
        prevBtn.addEventListener('click', () => {
          currentIndex = currentIndex > 0 ? currentIndex - 1 : slides.length - 1;
          updateCarousel();
        });
      }
      
      // Next button
      if (nextBtn) {
        nextBtn.addEventListener('click', () => {
          currentIndex = currentIndex < slides.length - 1 ? currentIndex + 1 : 0;
          updateCarousel();
        });
      }
      
      // Dot navigation
      dots.forEach((dot, index) => {
        dot.addEventListener('click', () => {
          currentIndex = index;
          updateCarousel();
        });
      });
      
      // Auto-play (optional)
      setInterval(() => {
        if (slides.length > 1) {
          currentIndex = currentIndex < slides.length - 1 ? currentIndex + 1 : 0;
          updateCarousel();
        }
      }, 5000); // Change slide every 5 seconds
    });
  });
</script>

<style>
  .carousel-slide {
    min-width: 100%;
  }
</style>
