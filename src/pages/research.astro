---
import Layout from '../components/Layout.astro';
import { readFileSync } from 'fs';
import { join } from 'path';

// Read research data
const researchPath = join(process.cwd(), 'data', 'research.json');
const researchData = JSON.parse(readFileSync(researchPath, 'utf-8'));
const { currentProjects, howToGetInvolved } = researchData;
---

<Layout title="Research - Pediatric POCUS Society">
  <section class="pt-32 pb-16 px-4">
    <div class="max-w-6xl mx-auto">
      <div class="text-center mb-16">
        <h1 class="section-title">Research Initiatives</h1>
        <p class="text-xl text-white/90 max-w-3xl mx-auto">
          Advancing pediatric point-of-care ultrasound through innovative research, 
          multi-center collaborations, and evidence-based practice development.
        </p>
      </div>
      
      <!-- Current Research Projects -->
      <div class="mb-20">
        <h2 class="text-3xl font-bold text-white mb-8 text-center">Current Research Projects</h2>
        <div class="space-y-8">
          {currentProjects.map((project: any, index: number) => (
            <div 
              class="glass-card card-hover"
              style={`animation: slideUp 0.6s ease-out ${index * 0.15}s both`}
            >
              <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div class="lg:col-span-2">
                  <div class="flex items-start justify-between mb-4">
                    <h3 class="text-xl font-bold text-white pr-4">{project.title}</h3>
                    <span class="status-active flex-shrink-0">
                      {project.status}
                    </span>
                  </div>
                  
                  <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                    <div>
                      <p class="text-white/70 text-sm">Principal Investigator</p>
                      <p class="text-white font-medium">{project.principalInvestigator}</p>
                    </div>
                    <div>
                      <p class="text-white/70 text-sm">Current Phase</p>
                      <p class="text-white font-medium">{project.currentPhase}</p>
                    </div>
                    <div>
                      <p class="text-white/70 text-sm">Participants</p>
                      <p class="text-white font-medium">{project.participants} enrolled</p>
                    </div>
                    <div>
                      <p class="text-white/70 text-sm">Publications</p>
                      <p class="text-white font-medium">{project.publications} published</p>
                    </div>
                  </div>
                  
                  <p class="text-white/90 text-sm mb-4">{project.description}</p>
                  
                  <div>
                    <h4 class="text-white font-semibold mb-2">Key Objectives:</h4>
                    <ul class="space-y-1">
                      {project.objectives.map((objective: string) => (
                        <li class="flex items-start text-white/80 text-sm">
                          <svg class="w-4 h-4 mr-2 mt-0.5 text-primary-300 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                          </svg>
                          {objective}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
                
                <div class="lg:border-l lg:border-white/20 lg:pl-6">
                  <div class="space-y-4">
                    <div class="glass rounded-lg p-4">
                      <p class="text-white/70 text-sm mb-1">Funding</p>
                      <p class="text-2xl font-bold text-secondary-300">{project.funding}</p>
                      <p class="text-white/80 text-sm">{project.fundingSource}</p>
                    </div>
                    
                    <div class="glass rounded-lg p-4">
                      <p class="text-white/70 text-sm mb-1">Timeline</p>
                      <p class="text-white font-medium text-sm">
                        {new Date(project.startDate).toLocaleDateString()} -
                        {new Date(project.endDate).toLocaleDateString()}
                      </p>
                    </div>

                    <div class="mt-4">
                      <a href="mailto:<EMAIL>" class="btn-secondary w-full text-sm">
                        Learn More
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
      
      <!-- How to Get Involved -->
      <div class="glass-card">
        <div class="text-center mb-8">
          <h2 class="text-3xl font-bold text-white mb-4">{howToGetInvolved.title}</h2>
          <p class="text-white/90 max-w-3xl mx-auto">{howToGetInvolved.description}</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          {howToGetInvolved.opportunities.map((opportunity: any, index: number) => (
            <div 
              class="glass rounded-xl p-6 card-hover"
              style={`animation: slideUp 0.6s ease-out ${index * 0.1}s both`}
            >
              <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-gradient-secondary rounded-full flex items-center justify-center mr-4">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2V6"></path>
                  </svg>
                </div>
                <h3 class="text-lg font-bold text-white">{opportunity.type}</h3>
              </div>
              
              <p class="text-white/90 text-sm mb-4">{opportunity.description}</p>
              
              <div class="mb-4">
                <h4 class="text-white font-medium mb-2">Requirements:</h4>
                <ul class="space-y-1">
                  {opportunity.requirements.map((req: string) => (
                    <li class="flex items-center text-white/80 text-sm">
                      <svg class="w-3 h-3 mr-2 text-primary-300" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                      </svg>
                      {req}
                    </li>
                  ))}
                </ul>
              </div>
              
              <a href={`mailto:${opportunity.contact}`} class="btn-primary w-full text-sm">
                Get Involved
              </a>
            </div>
          ))}
        </div>
        
        <div class="text-center mt-8 pt-8 border-t border-white/20">
          <h3 class="text-xl font-bold text-white mb-4">Ready to Contribute?</h3>
          <p class="text-white/90 mb-6">
            Join our research community and help advance pediatric POCUS through evidence-based practice.
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="mailto:<EMAIL>" class="btn-primary">
              Contact Research Team
            </a>
            <a href="/education" class="btn-secondary">
              View Training Programs
            </a>
          </div>
        </div>
      </div>
    </div>
  </section>
</Layout>
