import type { APIRoute } from 'astro';
import { readFileSync, writeFileSync } from 'fs';
import { join } from 'path';

// Helper function to check authentication
function isAuthenticated(request: Request): boolean {
  const cookies = request.headers.get('cookie');
  const authToken = cookies?.split(';').find(c => c.trim().startsWith('auth-token='))?.split('=')[1];
  return authToken === 'admin-authenticated';
}

// Get research data
export const GET: APIRoute = async () => {
  try {
    const researchPath = join(process.cwd(), 'data', 'research.json');
    const researchData = JSON.parse(readFileSync(researchPath, 'utf-8'));
    
    return new Response(JSON.stringify(researchData), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Error reading research data' 
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};

// Add new research project or opportunity
export const POST: APIRoute = async ({ request }) => {
  if (!isAuthenticated(request)) {
    return new Response(JSON.stringify({
      success: false,
      message: 'Unauthorized'
    }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  try {
    const body = await request.json();
    const { type, data } = body; // type: 'project' or 'opportunity'
    const researchPath = join(process.cwd(), 'data', 'research.json');
    const researchData = JSON.parse(readFileSync(researchPath, 'utf-8'));

    if (type === 'project') {
      // Generate new ID for project
      const newId = Math.max(0, ...researchData.currentProjects.map((p: any) => p.id)) + 1;
      const newProject = { ...data, id: newId };

      // Add to projects array
      researchData.currentProjects.push(newProject);
    } else if (type === 'opportunity') {
      // Add to opportunities array
      researchData.howToGetInvolved.opportunities.push(data);
    } else {
      // Backward compatibility - treat as project
      const newId = Math.max(0, ...researchData.currentProjects.map((p: any) => p.id)) + 1;
      const newProject = { ...body, id: newId };
      researchData.currentProjects.push(newProject);
    }

    // Write updated data to file
    writeFileSync(researchPath, JSON.stringify(researchData, null, 2), 'utf-8');

    return new Response(JSON.stringify({
      success: true,
      message: `Research ${type || 'project'} added successfully`,
      data: data
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    return new Response(JSON.stringify({
      success: false,
      message: 'Error adding research item'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};

// Update research project
export const PUT: APIRoute = async ({ request }) => {
  if (!isAuthenticated(request)) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Unauthorized' 
    }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  try {
    const body = await request.json();
    const researchPath = join(process.cwd(), 'data', 'research.json');
    const researchData = JSON.parse(readFileSync(researchPath, 'utf-8'));
    
    // Find and update the project
    const projectIndex = researchData.currentProjects.findIndex((p: any) => p.id === body.id);
    
    if (projectIndex === -1) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: 'Research project not found' 
      }), {
        status: 404,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
    
    // Update the project
    researchData.currentProjects[projectIndex] = body;
    
    // Write updated data to file
    writeFileSync(researchPath, JSON.stringify(researchData, null, 2), 'utf-8');
    
    return new Response(JSON.stringify({ 
      success: true, 
      message: 'Research project updated successfully',
      project: body
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Error updating research project' 
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};

// Delete research project
export const DELETE: APIRoute = async ({ request }) => {
  if (!isAuthenticated(request)) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Unauthorized' 
    }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  try {
    const url = new URL(request.url);
    const type = url.searchParams.get('type') || 'project';
    const id = url.searchParams.get('id');
    const index = parseInt(url.searchParams.get('index') || '0');

    const researchPath = join(process.cwd(), 'data', 'research.json');
    const researchData = JSON.parse(readFileSync(researchPath, 'utf-8'));

    if (type === 'project') {
      const projectId = parseInt(id || '0');
      if (!projectId) {
        return new Response(JSON.stringify({
          success: false,
          message: 'Research project ID required'
        }), {
          status: 400,
          headers: {
            'Content-Type': 'application/json'
          }
        });
      }

      // Find the project to delete
      const projectIndex = researchData.currentProjects.findIndex((p: any) => p.id === projectId);

      if (projectIndex === -1) {
        return new Response(JSON.stringify({
          success: false,
          message: 'Research project not found'
        }), {
          status: 404,
          headers: {
            'Content-Type': 'application/json'
          }
        });
      }

      // Remove the project
      researchData.currentProjects.splice(projectIndex, 1);
    } else if (type === 'opportunity') {
      // Remove opportunity by index
      if (index >= 0 && index < researchData.howToGetInvolved.opportunities.length) {
        researchData.howToGetInvolved.opportunities.splice(index, 1);
      } else {
        return new Response(JSON.stringify({
          success: false,
          message: 'Invalid opportunity index'
        }), {
          status: 400,
          headers: {
            'Content-Type': 'application/json'
          }
        });
      }
    }

    // Write updated data to file
    writeFileSync(researchPath, JSON.stringify(researchData, null, 2), 'utf-8');

    return new Response(JSON.stringify({
      success: true,
      message: `Research ${type} deleted successfully`
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Error deleting research project' 
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};
