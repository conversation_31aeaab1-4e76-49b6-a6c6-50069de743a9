import type { APIRoute } from 'astro';
import { readFileSync, writeFileSync } from 'fs';
import { join } from 'path';

// Helper function to check authentication
function isAuthenticated(request: Request): boolean {
  const cookies = request.headers.get('cookie');
  const authToken = cookies?.split(';').find(c => c.trim().startsWith('auth-token='))?.split('=')[1];
  return authToken === 'admin-authenticated';
}

// Get gallery data
export const GET: APIRoute = async () => {
  try {
    const galleryPath = join(process.cwd(), 'data', 'gallery.json');
    let galleryData;
    
    try {
      galleryData = JSON.parse(readFileSync(galleryPath, 'utf-8'));
    } catch (error) {
      // Create default gallery data if file doesn't exist
      galleryData = {
        title: "P2 Through the Years",
        description: "A visual journey through our organization's milestones and memorable moments",
        sections: []
      };
      writeFileSync(galleryPath, JSON.stringify(galleryData, null, 2), 'utf-8');
    }
    
    return new Response(JSON.stringify(galleryData), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Error reading gallery data' 
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};

// Update gallery data
export const PUT: APIRoute = async ({ request }) => {
  if (!isAuthenticated(request)) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Unauthorized' 
    }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  try {
    const body = await request.json();
    const galleryPath = join(process.cwd(), 'data', 'gallery.json');
    
    // Write updated gallery data to file
    writeFileSync(galleryPath, JSON.stringify(body, null, 2), 'utf-8');
    
    return new Response(JSON.stringify({ 
      success: true, 
      message: 'Gallery data updated successfully' 
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Error updating gallery data' 
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};

// Add new gallery section
export const POST: APIRoute = async ({ request }) => {
  if (!isAuthenticated(request)) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Unauthorized' 
    }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  try {
    const body = await request.json();
    const galleryPath = join(process.cwd(), 'data', 'gallery.json');
    const galleryData = JSON.parse(readFileSync(galleryPath, 'utf-8'));
    
    // Generate new ID
    const newId = Math.max(0, ...galleryData.sections.map((s: any) => s.id || 0)) + 1;
    const newSection = { ...body, id: newId };
    
    // Add to sections array
    galleryData.sections.push(newSection);
    
    // Write updated data to file
    writeFileSync(galleryPath, JSON.stringify(galleryData, null, 2), 'utf-8');
    
    return new Response(JSON.stringify({ 
      success: true, 
      message: 'Gallery section added successfully',
      section: newSection
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Error adding gallery section' 
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};

// Delete gallery section
export const DELETE: APIRoute = async ({ request }) => {
  if (!isAuthenticated(request)) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Unauthorized' 
    }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  try {
    const url = new URL(request.url);
    const sectionId = parseInt(url.searchParams.get('id') || '0');
    
    if (!sectionId) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: 'Section ID required' 
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
    
    const galleryPath = join(process.cwd(), 'data', 'gallery.json');
    const galleryData = JSON.parse(readFileSync(galleryPath, 'utf-8'));
    
    // Find and remove the section
    const sectionIndex = galleryData.sections.findIndex((s: any) => s.id === sectionId);
    
    if (sectionIndex === -1) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: 'Gallery section not found' 
      }), {
        status: 404,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
    
    // Remove the section
    galleryData.sections.splice(sectionIndex, 1);
    
    // Write updated data to file
    writeFileSync(galleryPath, JSON.stringify(galleryData, null, 2), 'utf-8');
    
    return new Response(JSON.stringify({ 
      success: true, 
      message: 'Gallery section deleted successfully' 
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Error deleting gallery section' 
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};
