import type { APIRoute } from 'astro';

// Simple authentication endpoint
export const POST: APIRoute = async ({ request }) => {
  try {
    const body = await request.json();
    const { username, password } = body;
    
    // Simple hardcoded authentication (as requested)
    if (username === 'admin' && password === 'password') {
      return new Response(JSON.stringify({ 
        success: true, 
        message: 'Authentication successful',
        token: 'admin-authenticated' // Simple token for session management
      }), {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Set-Cookie': 'auth-token=admin-authenticated; HttpOnly; Path=/; Max-Age=86400' // 24 hours
        }
      });
    } else {
      return new Response(JSON.stringify({ 
        success: false, 
        message: 'Invalid credentials' 
      }), {
        status: 401,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
  } catch (error) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Authentication error' 
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};

// Logout endpoint
export const DELETE: APIRoute = async () => {
  return new Response(JSON.stringify({ 
    success: true, 
    message: 'Logged out successfully' 
  }), {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
      'Set-Cookie': 'auth-token=; HttpOnly; Path=/; Max-Age=0' // Clear cookie
    }
  });
};

// Check authentication status
export const GET: APIRoute = async ({ request }) => {
  const cookies = request.headers.get('cookie');
  const authToken = cookies?.split(';').find(c => c.trim().startsWith('auth-token='))?.split('=')[1];
  
  if (authToken === 'admin-authenticated') {
    return new Response(JSON.stringify({ 
      authenticated: true,
      user: 'admin'
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } else {
    return new Response(JSON.stringify({ 
      authenticated: false 
    }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};
