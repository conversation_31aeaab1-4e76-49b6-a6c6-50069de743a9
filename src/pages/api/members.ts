import type { APIRoute } from 'astro';
import { readFileSync, writeFileSync } from 'fs';
import { join } from 'path';

// Helper function to check authentication
function isAuthenticated(request: Request): boolean {
  const cookies = request.headers.get('cookie');
  const authToken = cookies?.split(';').find(c => c.trim().startsWith('auth-token='))?.split('=')[1];
  return authToken === 'admin-authenticated';
}

// Get members data
export const GET: APIRoute = async () => {
  try {
    const membersPath = join(process.cwd(), 'data', 'members.json');
    const membersData = JSON.parse(readFileSync(membersPath, 'utf-8'));
    
    return new Response(JSON.stringify(membersData), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Error reading members data' 
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};

// Add new member
export const POST: APIRoute = async ({ request }) => {
  if (!isAuthenticated(request)) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Unauthorized' 
    }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  try {
    const newMember = await request.json();
    const membersPath = join(process.cwd(), 'data', 'members.json');
    const membersData = JSON.parse(readFileSync(membersPath, 'utf-8'));
    
    // Generate new ID
    const maxId = Math.max(...membersData.boardMembers.map((m: any) => m.id), 0);
    newMember.id = maxId + 1;
    
    // Add new member
    membersData.boardMembers.push(newMember);
    
    // Write updated data to file
    writeFileSync(membersPath, JSON.stringify(membersData, null, 2), 'utf-8');
    
    return new Response(JSON.stringify({ 
      success: true, 
      message: 'Member added successfully',
      member: newMember
    }), {
      status: 201,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Error adding member' 
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};

// Update member
export const PUT: APIRoute = async ({ request }) => {
  if (!isAuthenticated(request)) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Unauthorized' 
    }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  try {
    const updatedMember = await request.json();
    const membersPath = join(process.cwd(), 'data', 'members.json');
    const membersData = JSON.parse(readFileSync(membersPath, 'utf-8'));
    
    // Find and update member
    const memberIndex = membersData.boardMembers.findIndex((m: any) => m.id === updatedMember.id);
    if (memberIndex === -1) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: 'Member not found' 
      }), {
        status: 404,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
    
    membersData.boardMembers[memberIndex] = updatedMember;
    
    // Write updated data to file
    writeFileSync(membersPath, JSON.stringify(membersData, null, 2), 'utf-8');
    
    return new Response(JSON.stringify({ 
      success: true, 
      message: 'Member updated successfully',
      member: updatedMember
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Error updating member' 
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};

// Delete member
export const DELETE: APIRoute = async ({ request }) => {
  if (!isAuthenticated(request)) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Unauthorized' 
    }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  try {
    const url = new URL(request.url);
    const memberId = parseInt(url.searchParams.get('id') || '0');
    
    if (!memberId) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: 'Member ID required' 
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
    
    const membersPath = join(process.cwd(), 'data', 'members.json');
    const membersData = JSON.parse(readFileSync(membersPath, 'utf-8'));
    
    // Remove member
    membersData.boardMembers = membersData.boardMembers.filter((m: any) => m.id !== memberId);
    
    // Write updated data to file
    writeFileSync(membersPath, JSON.stringify(membersData, null, 2), 'utf-8');
    
    return new Response(JSON.stringify({ 
      success: true, 
      message: 'Member deleted successfully'
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Error deleting member' 
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};
