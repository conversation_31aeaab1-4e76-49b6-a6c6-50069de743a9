import type { APIRoute } from 'astro';
import { readFileSync, writeFileSync } from 'fs';
import { join } from 'path';

// Helper function to check authentication
function isAuthenticated(request: Request): boolean {
  const cookies = request.headers.get('cookie');
  const authToken = cookies?.split(';').find(c => c.trim().startsWith('auth-token='))?.split('=')[1];
  return authToken === 'admin-authenticated';
}

// Get education data
export const GET: APIRoute = async () => {
  try {
    const educationPath = join(process.cwd(), 'data', 'education.json');
    const educationData = JSON.parse(readFileSync(educationPath, 'utf-8'));
    
    return new Response(JSON.stringify(educationData), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Error reading education data' 
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};

// Update education data
export const PUT: APIRoute = async ({ request }) => {
  if (!isAuthenticated(request)) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Unauthorized' 
    }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  try {
    const body = await request.json();
    const educationPath = join(process.cwd(), 'data', 'education.json');
    
    // Write updated education data to file
    writeFileSync(educationPath, JSON.stringify(body, null, 2), 'utf-8');
    
    return new Response(JSON.stringify({ 
      success: true, 
      message: 'Education data updated successfully' 
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Error updating education data' 
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};

// Add new fellowship program
export const POST: APIRoute = async ({ request }) => {
  if (!isAuthenticated(request)) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Unauthorized' 
    }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  try {
    const body = await request.json();
    const { type, data } = body; // type: 'fellowship', 'trainingResource', 'certificationLevel'
    
    const educationPath = join(process.cwd(), 'data', 'education.json');
    const educationData = JSON.parse(readFileSync(educationPath, 'utf-8'));
    
    if (type === 'fellowship') {
      const newId = Math.max(0, ...educationData.fellowshipPrograms.map((p: any) => p.id)) + 1;
      educationData.fellowshipPrograms.push({ ...data, id: newId });
    } else if (type === 'trainingResource') {
      educationData.trainingResources.push(data);
    } else if (type === 'certificationLevel') {
      educationData.certificationInfo.levels.push(data);
    }
    
    // Write updated data to file
    writeFileSync(educationPath, JSON.stringify(educationData, null, 2), 'utf-8');
    
    return new Response(JSON.stringify({ 
      success: true, 
      message: `${type} added successfully`,
      data: data
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Error adding education item' 
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};

// Delete education item
export const DELETE: APIRoute = async ({ request }) => {
  if (!isAuthenticated(request)) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Unauthorized' 
    }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  try {
    const url = new URL(request.url);
    const type = url.searchParams.get('type');
    const id = url.searchParams.get('id');
    const index = parseInt(url.searchParams.get('index') || '0');
    
    if (!type) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: 'Type parameter required' 
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
    
    const educationPath = join(process.cwd(), 'data', 'education.json');
    const educationData = JSON.parse(readFileSync(educationPath, 'utf-8'));
    
    if (type === 'fellowship' && id) {
      const fellowshipIndex = educationData.fellowshipPrograms.findIndex((p: any) => p.id === parseInt(id));
      if (fellowshipIndex !== -1) {
        educationData.fellowshipPrograms.splice(fellowshipIndex, 1);
      }
    } else if (type === 'trainingResource') {
      educationData.trainingResources.splice(index, 1);
    } else if (type === 'certificationLevel') {
      educationData.certificationInfo.levels.splice(index, 1);
    }
    
    // Write updated data to file
    writeFileSync(educationPath, JSON.stringify(educationData, null, 2), 'utf-8');
    
    return new Response(JSON.stringify({ 
      success: true, 
      message: `${type} deleted successfully` 
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Error deleting education item' 
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};
