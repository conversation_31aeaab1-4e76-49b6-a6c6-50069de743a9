import type { APIRoute } from 'astro';
import { readFileSync, writeFileSync } from 'fs';
import { join } from 'path';

// Helper function to check authentication
function isAuthenticated(request: Request): boolean {
  const cookies = request.headers.get('cookie');
  const authToken = cookies?.split(';').find(c => c.trim().startsWith('auth-token='))?.split('=')[1];
  return authToken === 'admin-authenticated';
}

// Get content data
export const GET: APIRoute = async () => {
  try {
    const contentPath = join(process.cwd(), 'data', 'content.json');
    const contentData = JSON.parse(readFileSync(contentPath, 'utf-8'));
    
    return new Response(JSON.stringify(contentData), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Error reading content data' 
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};

// Update content data
export const PUT: APIRoute = async ({ request }) => {
  if (!isAuthenticated(request)) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Unauthorized' 
    }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  try {
    const body = await request.json();
    const contentPath = join(process.cwd(), 'data', 'content.json');
    
    // Write updated content to file
    writeFileSync(contentPath, JSON.stringify(body, null, 2), 'utf-8');
    
    return new Response(JSON.stringify({ 
      success: true, 
      message: 'Content updated successfully' 
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Error updating content data' 
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};
