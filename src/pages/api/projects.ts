import type { APIRoute } from 'astro';
import { readFileSync, writeFileSync } from 'fs';
import { join } from 'path';

// Helper function to check authentication
function isAuthenticated(request: Request): boolean {
  const cookies = request.headers.get('cookie');
  const authToken = cookies?.split(';').find(c => c.trim().startsWith('auth-token='))?.split('=')[1];
  return authToken === 'admin-authenticated';
}

// Get projects data
export const GET: APIRoute = async () => {
  try {
    const projectsPath = join(process.cwd(), 'data', 'projects.json');
    const projectsData = JSON.parse(readFileSync(projectsPath, 'utf-8'));
    
    return new Response(JSON.stringify(projectsData), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Error reading projects data' 
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};

// Add new project
export const POST: APIRoute = async ({ request }) => {
  if (!isAuthenticated(request)) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Unauthorized' 
    }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  try {
    const newProject = await request.json();
    const projectsPath = join(process.cwd(), 'data', 'projects.json');
    const projectsData = JSON.parse(readFileSync(projectsPath, 'utf-8'));
    
    // Generate new ID
    const maxId = Math.max(...projectsData.globalHealthProjects.map((p: any) => p.id), 0);
    newProject.id = maxId + 1;
    
    // Add new project
    projectsData.globalHealthProjects.push(newProject);
    
    // Write updated data to file
    writeFileSync(projectsPath, JSON.stringify(projectsData, null, 2), 'utf-8');
    
    return new Response(JSON.stringify({ 
      success: true, 
      message: 'Project added successfully',
      project: newProject
    }), {
      status: 201,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Error adding project' 
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};

// Update project
export const PUT: APIRoute = async ({ request }) => {
  if (!isAuthenticated(request)) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Unauthorized' 
    }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  try {
    const updatedProject = await request.json();
    const projectsPath = join(process.cwd(), 'data', 'projects.json');
    const projectsData = JSON.parse(readFileSync(projectsPath, 'utf-8'));
    
    // Find and update project
    const projectIndex = projectsData.globalHealthProjects.findIndex((p: any) => p.id === updatedProject.id);
    if (projectIndex === -1) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: 'Project not found' 
      }), {
        status: 404,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
    
    projectsData.globalHealthProjects[projectIndex] = updatedProject;
    
    // Write updated data to file
    writeFileSync(projectsPath, JSON.stringify(projectsData, null, 2), 'utf-8');
    
    return new Response(JSON.stringify({ 
      success: true, 
      message: 'Project updated successfully',
      project: updatedProject
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Error updating project' 
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};

// Delete project
export const DELETE: APIRoute = async ({ request }) => {
  if (!isAuthenticated(request)) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Unauthorized' 
    }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  try {
    const url = new URL(request.url);
    const projectId = parseInt(url.searchParams.get('id') || '0');
    
    if (!projectId) {
      return new Response(JSON.stringify({ 
        success: false, 
        message: 'Project ID required' 
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
    
    const projectsPath = join(process.cwd(), 'data', 'projects.json');
    const projectsData = JSON.parse(readFileSync(projectsPath, 'utf-8'));
    
    // Remove project
    projectsData.globalHealthProjects = projectsData.globalHealthProjects.filter((p: any) => p.id !== projectId);
    
    // Write updated data to file
    writeFileSync(projectsPath, JSON.stringify(projectsData, null, 2), 'utf-8');
    
    return new Response(JSON.stringify({ 
      success: true, 
      message: 'Project deleted successfully'
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    return new Response(JSON.stringify({ 
      success: false, 
      message: 'Error deleting project' 
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};
