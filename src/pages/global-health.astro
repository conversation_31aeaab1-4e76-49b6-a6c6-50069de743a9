---
import Layout from '../components/Layout.astro';
import { fetchProjectsData } from '../utils/api.js';

// Fetch projects data from PHP API
const projectsData = await fetchProjectsData();
const { globalHealthProjects } = projectsData;

// Helper function to get status class
function getStatusClass(status: string) {
  switch (status.toLowerCase()) {
    case 'active':
      return 'status-active';
    case 'planning':
      return 'status-planning';
    case 'completed':
      return 'status-completed';
    default:
      return 'status-active';
  }
}
---

<Layout title="Global Health - Pediatric POCUS Society">
  <section class="pt-32 pb-16 px-4">
    <div class="max-w-7xl mx-auto">
      <div class="text-center mb-16">
        <h1 class="section-title">Global Health Initiatives</h1>
        <p class="text-xl text-white/90 max-w-3xl mx-auto">
          Expanding access to pediatric point-of-care ultrasound worldwide through 
          training programs, capacity building, and sustainable healthcare partnerships.
        </p>
      </div>
      
      <!-- Summary Cards -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
        <div class="glass-card text-center">
          <div class="text-3xl font-bold text-secondary-300 mb-2">
            {globalHealthProjects.length}
          </div>
          <p class="text-white/80">Total Projects</p>
        </div>
        <div class="glass-card text-center">
          <div class="text-3xl font-bold text-green-300 mb-2">
            {globalHealthProjects.filter(p => p.status === 'Active').length}
          </div>
          <p class="text-white/80">Active Projects</p>
        </div>
        <div class="glass-card text-center">
          <div class="text-3xl font-bold text-primary-300 mb-2">
            {globalHealthProjects.reduce((sum, p) => sum + p.participants, 0)}
          </div>
          <p class="text-white/80">Total Participants</p>
        </div>
        <div class="glass-card text-center">
          <div class="text-3xl font-bold text-yellow-300 mb-2">
            {new Set(globalHealthProjects.map(p => p.location.split(',')[1]?.trim())).size}
          </div>
          <p class="text-white/80">Countries</p>
        </div>
      </div>
      
      <!-- Projects Table -->
      <div class="glass-table mb-12">
        <div class="overflow-x-auto">
          <table class="w-full">
            <thead>
              <tr>
                <th class="text-left">Project</th>
                <th class="text-left">Location</th>
                <th class="text-left">Status</th>
                <th class="text-left">Lead Investigator</th>
                <th class="text-left">Timeline</th>
                <th class="text-left">Participants</th>
                <th class="text-left">Budget</th>
                <th class="text-left">Outcomes</th>
              </tr>
            </thead>
            <tbody>
              {globalHealthProjects.map((project: any) => (
                <tr class="hover:bg-white/5 transition-colors">
                  <td class="font-medium">
                    <div>
                      <p class="text-white font-semibold">{project.title}</p>
                      <p class="text-white/70 text-sm mt-1">{project.description}</p>
                    </div>
                  </td>
                  <td>
                    <div class="flex items-center">
                      <svg class="w-4 h-4 mr-2 text-primary-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                      </svg>
                      {project.location}
                    </div>
                  </td>
                  <td>
                    <span class={getStatusClass(project.status)}>
                      {project.status}
                    </span>
                  </td>
                  <td class="text-white/90">{project.leadInvestigator}</td>
                  <td>
                    <div class="text-sm">
                      <p class="text-white/90">{new Date(project.startDate).toLocaleDateString()}</p>
                      <p class="text-white/70">to {new Date(project.endDate).toLocaleDateString()}</p>
                    </div>
                  </td>
                  <td class="text-center">
                    <div class="text-lg font-semibold text-secondary-300">
                      {project.participants}
                    </div>
                  </td>
                  <td class="text-primary-300 font-medium">{project.budget}</td>
                  <td class="text-white/90 text-sm max-w-xs">
                    {project.outcomes}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
      
      <!-- Project Details Cards (Mobile-friendly alternative) -->
      <div class="block md:hidden space-y-6 mb-12">
        {globalHealthProjects.map((project: any, index: number) => (
          <div 
            class="glass-card"
            style={`animation: slideUp 0.6s ease-out ${index * 0.1}s both`}
          >
            <div class="flex items-start justify-between mb-4">
              <h3 class="text-lg font-bold text-white pr-4">{project.title}</h3>
              <span class={getStatusClass(project.status)}>
                {project.status}
              </span>
            </div>
            
            <p class="text-white/90 text-sm mb-4">{project.description}</p>
            
            <div class="grid grid-cols-2 gap-4 mb-4">
              <div>
                <p class="text-white/70 text-sm">Location</p>
                <p class="text-white font-medium">{project.location}</p>
              </div>
              <div>
                <p class="text-white/70 text-sm">Lead Investigator</p>
                <p class="text-white font-medium">{project.leadInvestigator}</p>
              </div>
              <div>
                <p class="text-white/70 text-sm">Participants</p>
                <p class="text-secondary-300 font-semibold">{project.participants}</p>
              </div>
              <div>
                <p class="text-white/70 text-sm">Budget</p>
                <p class="text-primary-300 font-medium">{project.budget}</p>
              </div>
            </div>
            
            <div class="mb-4">
              <p class="text-white/70 text-sm">Timeline</p>
              <p class="text-white font-medium">
                {new Date(project.startDate).toLocaleDateString()} - {new Date(project.endDate).toLocaleDateString()}
              </p>
            </div>
            
            <div>
              <p class="text-white/70 text-sm mb-2">Outcomes</p>
              <p class="text-white/90 text-sm">{project.outcomes}</p>
            </div>
          </div>
        ))}
      </div>
      
      <!-- Call to Action -->
      <div class="glass-card text-center">
        <h2 class="text-3xl font-bold text-white mb-4">Join Our Global Mission</h2>
        <p class="text-white/90 mb-8 max-w-3xl mx-auto">
          Help us expand access to pediatric POCUS worldwide. Whether you're interested in 
          leading a project, participating in training programs, or supporting our initiatives, 
          there are many ways to get involved.
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div class="glass rounded-xl p-6">
            <div class="w-12 h-12 bg-gradient-primary rounded-full mx-auto mb-4 flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2V6"></path>
              </svg>
            </div>
            <h3 class="text-lg font-bold text-white mb-2">Lead a Project</h3>
            <p class="text-white/80 text-sm">Propose and lead your own global health initiative</p>
          </div>
          
          <div class="glass rounded-xl p-6">
            <div class="w-12 h-12 bg-gradient-secondary rounded-full mx-auto mb-4 flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
              </svg>
            </div>
            <h3 class="text-lg font-bold text-white mb-2">Provide Training</h3>
            <p class="text-white/80 text-sm">Share your expertise through international training programs</p>
          </div>
          
          <div class="glass rounded-xl p-6">
            <div class="w-12 h-12 bg-gradient-primary rounded-full mx-auto mb-4 flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-bold text-white mb-2">Support Initiatives</h3>
            <p class="text-white/80 text-sm">Contribute funding or resources to ongoing projects</p>
          </div>
        </div>
        
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a href="mailto:<EMAIL>" class="btn-primary">
            Get Involved
          </a>
          <a href="/education" class="btn-secondary">
            View Training Programs
          </a>
        </div>
      </div>
    </div>
  </section>
</Layout>

<style>
  /* Hide table on mobile, show cards instead */
  @media (max-width: 768px) {
    .glass-table {
      display: none;
    }
  }
  
  /* Show table on desktop, hide cards */
  @media (min-width: 769px) {
    .block.md\:hidden {
      display: none !important;
    }
  }
</style>
