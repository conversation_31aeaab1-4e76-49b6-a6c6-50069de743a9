---
import Layout from '../components/Layout.astro';
import { fetchContentData } from '../utils/api.js';

// Fetch content data from PHP API
const contentData = await fetchContentData();
const { adminResources } = contentData;

// Group links by category
const groupedLinks = adminResources.links.reduce((acc: any, link: any) => {
  if (!acc[link.category]) {
    acc[link.category] = [];
  }
  acc[link.category].push(link);
  return acc;
}, {});
---

<Layout title="Admin Resources - Pediatric POCUS Society">
  <section class="pt-32 pb-16 px-4">
    <div class="max-w-6xl mx-auto">
      <div class="text-center mb-16">
        <h1 class="section-title">{adminResources.title}</h1>
        <p class="text-xl text-white/90 max-w-3xl mx-auto">
          {adminResources.description}
        </p>
      </div>
      
      <!-- Quick Access Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
        <div class="glass-card card-hover text-center">
          <div class="w-16 h-16 bg-gradient-primary rounded-full mx-auto mb-4 flex items-center justify-center">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
          </div>
          <h3 class="text-xl font-bold text-white mb-2">Policies & Guidelines</h3>
          <p class="text-white/80 text-sm mb-4">Access organizational policies and research guidelines</p>
          <span class="bg-primary-500/20 text-primary-200 px-3 py-1 rounded-full text-sm">
            {groupedLinks.Policy?.length || 0} Documents
          </span>
        </div>
        
        <div class="glass-card card-hover text-center">
          <div class="w-16 h-16 bg-gradient-secondary rounded-full mx-auto mb-4 flex items-center justify-center">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
          </div>
          <h3 class="text-xl font-bold text-white mb-2">Research Papers</h3>
          <p class="text-white/80 text-sm mb-4">Latest research publications and reports</p>
          <span class="bg-secondary-500/20 text-secondary-200 px-3 py-1 rounded-full text-sm">
            {groupedLinks.Research?.length || 0} Publications
          </span>
        </div>
        
        <div class="glass-card card-hover text-center">
          <div class="w-16 h-16 bg-gradient-primary rounded-full mx-auto mb-4 flex items-center justify-center">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"></path>
            </svg>
          </div>
          <h3 class="text-xl font-bold text-white mb-2">Statements</h3>
          <p class="text-white/80 text-sm mb-4">Official organizational statements and positions</p>
          <span class="bg-primary-500/20 text-primary-200 px-3 py-1 rounded-full text-sm">
            {groupedLinks.Statement?.length || 0} Statements
          </span>
        </div>
      </div>
      
      <!-- Resource Categories -->
      {Object.entries(groupedLinks).map(([category, links]: [string, any]) => (
        <div class="mb-12">
          <h2 class="text-2xl font-bold text-white mb-6 flex items-center">
            <div class="w-8 h-8 bg-gradient-secondary rounded-full mr-3 flex items-center justify-center">
              <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
            </div>
            {category}
          </h2>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            {links.map((link: any, index: number) => (
              <div 
                class="glass-card card-hover"
                style={`animation: slideUp 0.6s ease-out ${index * 0.1}s both`}
              >
                <div class="flex items-start justify-between mb-4">
                  <h3 class="text-lg font-bold text-white pr-4">{link.title}</h3>
                  <span class="bg-white/10 text-white/70 px-2 py-1 rounded text-xs flex-shrink-0">
                    PDF
                  </span>
                </div>
                
                <p class="text-white/90 text-sm mb-4">{link.description}</p>
                
                <div class="flex items-center justify-between">
                  <span class="text-primary-300 text-sm font-medium">{link.category}</span>
                  <a 
                    href={link.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    class="btn-secondary text-sm flex items-center"
                  >
                    Download
                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                  </a>
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}
      
      <!-- Additional Resources -->
      <div class="glass-card">
        <h2 class="text-2xl font-bold text-white mb-6 text-center">Additional Resources</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <div class="glass rounded-xl p-6 text-center">
            <div class="w-12 h-12 bg-gradient-primary rounded-full mx-auto mb-4 flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-bold text-white mb-2">Member Directory</h3>
            <p class="text-white/80 text-sm mb-4">Access contact information for all members</p>
            <a href="mailto:<EMAIL>" class="text-primary-300 hover:text-primary-200 text-sm">
              Request Access
            </a>
          </div>
          
          <div class="glass rounded-xl p-6 text-center">
            <div class="w-12 h-12 bg-gradient-secondary rounded-full mx-auto mb-4 flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-bold text-white mb-2">Meeting Minutes</h3>
            <p class="text-white/80 text-sm mb-4">Board meeting minutes and committee reports</p>
            <a href="mailto:<EMAIL>" class="text-primary-300 hover:text-primary-200 text-sm">
              Request Access
            </a>
          </div>
          
          <div class="glass rounded-xl p-6 text-center">
            <div class="w-12 h-12 bg-gradient-primary rounded-full mx-auto mb-4 flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-bold text-white mb-2">Financial Reports</h3>
            <p class="text-white/80 text-sm mb-4">Annual financial statements and budgets</p>
            <a href="mailto:<EMAIL>" class="text-primary-300 hover:text-primary-200 text-sm">
              Request Access
            </a>
          </div>
        </div>
        
        <div class="text-center pt-8 border-t border-white/20">
          <h3 class="text-xl font-bold text-white mb-4">Need Help?</h3>
          <p class="text-white/90 mb-6">
            Can't find what you're looking for? Contact our administrative team for assistance.
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="mailto:<EMAIL>" class="btn-primary">
              Contact Admin Team
            </a>
            <a href="/admin-panel" class="btn-secondary">
              Admin Panel
            </a>
          </div>
        </div>
      </div>
    </div>
  </section>
</Layout>
