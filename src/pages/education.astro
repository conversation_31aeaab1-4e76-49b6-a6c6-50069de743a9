---
import Layout from '../components/Layout.astro';
import { fetchEducationData } from '../utils/api.js';

// Fetch education data from PHP API
const educationData = await fetchEducationData();
const { fellowshipPrograms, trainingResources, certificationInfo } = educationData;
---

<Layout title="Education - Pediatric POCUS Society">
  <section class="pt-32 pb-16 px-4">
    <div class="max-w-6xl mx-auto">
      <div class="text-center mb-16">
        <h1 class="section-title">Education & Training</h1>
        <p class="text-xl text-white/90 max-w-3xl mx-auto">
          Comprehensive educational programs designed to advance pediatric POCUS expertise 
          through fellowships, training resources, and certification programs.
        </p>
      </div>
      
      <!-- Fellowship Programs -->
      <div class="mb-20">
        <h2 class="text-3xl font-bold text-white mb-8 text-center">Fellowship Programs</h2>
        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
          {fellowshipPrograms.map((program: any, index: number) => (
            <div 
              class="glass-card card-hover"
              style={`animation: slideUp 0.6s ease-out ${index * 0.2}s both`}
            >
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-xl font-bold text-white">{program.title}</h3>
                <span class="bg-secondary-500/20 text-secondary-200 px-3 py-1 rounded-full text-sm font-medium">
                  {program.duration}
                </span>
              </div>
              
              <div class="space-y-3 mb-6">
                <div class="flex items-center text-white/80 text-sm">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                  </svg>
                  {program.location}
                </div>
                <div class="flex items-center text-white/80 text-sm">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                  </svg>
                  Stipend: {program.stipend}
                </div>
                <div class="flex items-center text-white/80 text-sm">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                  </svg>
                  {program.positions} positions available
                </div>
              </div>
              
              <p class="text-white/90 text-sm mb-4">{program.description}</p>
              
              <div class="mb-4">
                <h4 class="text-white font-semibold mb-2">Curriculum Highlights:</h4>
                <ul class="text-white/80 text-sm space-y-1">
                  {program.curriculum.slice(0, 3).map((item: string) => (
                    <li class="flex items-center">
                      <svg class="w-3 h-3 mr-2 text-primary-300" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                      </svg>
                      {item}
                    </li>
                  ))}
                </ul>
              </div>
              
              <div class="flex items-center justify-between pt-4 border-t border-white/20">
                <div class="text-sm">
                  <p class="text-white/70">Application Deadline</p>
                  <p class="text-white font-medium">{new Date(program.applicationDeadline).toLocaleDateString()}</p>
                </div>
                <a href="mailto:<EMAIL>" class="btn-secondary text-sm">
                  Apply Now
                </a>
              </div>
            </div>
          ))}
        </div>
      </div>
      
      <!-- Training Resources -->
      <div class="mb-20">
        <h2 class="text-3xl font-bold text-white mb-8 text-center">Training Resources</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {trainingResources.map((resource: any, index: number) => (
            <div 
              class="glass-card card-hover text-center"
              style={`animation: slideUp 0.6s ease-out ${index * 0.15}s both`}
            >
              <div class="w-16 h-16 bg-gradient-primary rounded-full mx-auto mb-4 flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                </svg>
              </div>
              <h3 class="text-lg font-bold text-white mb-2">{resource.title}</h3>
              <div class="flex items-center justify-center space-x-4 mb-3">
                <span class="bg-primary-500/20 text-primary-200 px-2 py-1 rounded text-xs">
                  {resource.type}
                </span>
                <span class="text-white/70 text-sm">{resource.duration}</span>
              </div>
              <p class="text-secondary-300 font-semibold mb-3">{resource.cost}</p>
              <p class="text-white/90 text-sm mb-4">{resource.description}</p>
              
              {resource.modules && (
                <div class="text-left mb-4">
                  <h4 class="text-white font-medium mb-2">Modules:</h4>
                  <ul class="text-white/80 text-sm space-y-1">
                    {resource.modules.slice(0, 3).map((module: string) => (
                      <li class="flex items-center">
                        <svg class="w-3 h-3 mr-2 text-secondary-300" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        {module}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              
              <a href="#" class="btn-primary w-full text-sm">
                Access Resource
              </a>
            </div>
          ))}
        </div>
      </div>
      
      <!-- Certification Information -->
      <div class="glass-card">
        <h2 class="text-3xl font-bold text-white mb-8 text-center">Certification Program</h2>
        <p class="text-white/90 text-center mb-8 max-w-3xl mx-auto">
          {certificationInfo.description}
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          {certificationInfo.levels.map((level: any, index: number) => (
            <div 
              class="glass rounded-xl p-6 text-center card-hover"
              style={`animation: slideUp 0.6s ease-out ${index * 0.2}s both`}
            >
              <h3 class="text-xl font-bold text-white mb-4">{level.name}</h3>
              <div class="text-2xl font-bold text-secondary-300 mb-4">{level.cost}</div>
              <div class="text-white/70 text-sm mb-6">Valid for {level.validityPeriod}</div>
              
              <div class="text-left mb-6">
                <h4 class="text-white font-medium mb-3">Requirements:</h4>
                <ul class="space-y-2">
                  {level.requirements.map((req: string) => (
                    <li class="flex items-start text-white/80 text-sm">
                      <svg class="w-4 h-4 mr-2 mt-0.5 text-primary-300 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                      </svg>
                      {req}
                    </li>
                  ))}
                </ul>
              </div>
              
              <a href="mailto:<EMAIL>" class="btn-secondary w-full text-sm">
                Apply for Certification
              </a>
            </div>
          ))}
        </div>
      </div>
    </div>
  </section>

  <!-- Learning Resources Section -->
  {educationData.learningResources && (
    <section class="py-16 px-4">
      <div class="max-w-6xl mx-auto">
        <div class="text-center mb-16">
          <h2 class="section-title">{educationData.learningResources.title}</h2>
          <p class="text-xl text-white/90 max-w-3xl mx-auto">
            {educationData.learningResources.description}
          </p>
        </div>

        <div class="grid lg:grid-cols-3 gap-8">
          <!-- Past Webinars -->
          <div class="glass-card">
            <div class="flex items-center mb-6">
              <div class="w-12 h-12 bg-gradient-primary rounded-lg flex items-center justify-center mr-4">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                </svg>
              </div>
              <h3 class="text-2xl font-bold text-white">Past Webinars</h3>
            </div>

            <div class="relative">
              <div class="carousel-container overflow-hidden" data-carousel="webinars">
                <div class="carousel-track flex transition-transform duration-500 ease-in-out">
                  {educationData.learningResources.pastWebinars?.map((webinar: any) => (
                    <div class="carousel-slide flex-shrink-0 w-full">
                      <div class="bg-white/5 rounded-lg p-4 border border-white/10 mx-2">
                        <h4 class="font-semibold text-white mb-2">{webinar.title}</h4>
                        <div class="text-sm text-white/70 mb-2">
                          <span class="mr-4">📅 {new Date(webinar.date).toLocaleDateString()}</span>
                          <span class="mr-4">⏱️ {webinar.duration}</span>
                        </div>
                        <p class="text-sm text-white/80 mb-2">Presenter: {webinar.presenter}</p>
                        <p class="text-sm text-white/70 mb-3">{webinar.description}</p>
                        <div class="flex gap-2">
                          {webinar.videoUrl && (
                            <a href={webinar.videoUrl} class="text-xs bg-primary-600 hover:bg-primary-700 text-white px-3 py-1 rounded-full transition-colors">
                              Watch Video
                            </a>
                          )}
                          {webinar.slides && (
                            <a href={webinar.slides} class="text-xs bg-white/10 hover:bg-white/20 text-white px-3 py-1 rounded-full transition-colors">
                              Download Slides
                            </a>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {educationData.learningResources.pastWebinars?.length > 1 && (
                <div class="flex items-center justify-between mt-4">
                  <button class="carousel-prev bg-white/10 hover:bg-white/20 text-white p-2 rounded-full transition-colors" data-carousel="webinars">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                  </button>

                  <div class="flex space-x-1">
                    {educationData.learningResources.pastWebinars?.map((_: any, index: number) => (
                      <button class={`carousel-dot w-2 h-2 rounded-full transition-colors ${index === 0 ? 'bg-white' : 'bg-white/40'}`} data-carousel="webinars" data-index={index}></button>
                    ))}
                  </div>

                  <button class="carousel-next bg-white/10 hover:bg-white/20 text-white p-2 rounded-full transition-colors" data-carousel="webinars">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                  </button>
                </div>
              )}
            </div>
          </div>

          <!-- Past Conferences -->
          <div class="glass-card">
            <div class="flex items-center mb-6">
              <div class="w-12 h-12 bg-gradient-secondary rounded-lg flex items-center justify-center mr-4">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
              </div>
              <h3 class="text-2xl font-bold text-white">Past Conferences</h3>
            </div>

            <div class="relative">
              <div class="carousel-container overflow-hidden" data-carousel="conferences">
                <div class="carousel-track flex transition-transform duration-500 ease-in-out">
                  {educationData.learningResources.pastConferences?.map((conference: any) => (
                    <div class="carousel-slide flex-shrink-0 w-full">
                      <div class="bg-white/5 rounded-lg p-4 border border-white/10 mx-2">
                        <h4 class="font-semibold text-white mb-2">{conference.title}</h4>
                        <div class="text-sm text-white/70 mb-2">
                          <span class="mr-4">📅 {new Date(conference.date).toLocaleDateString()}</span>
                          <span>📍 {conference.location}</span>
                        </div>
                        <p class="text-sm text-white/70 mb-3">{conference.description}</p>

                        {conference.highlights && (
                          <div class="mb-3">
                            <p class="text-sm font-medium text-white/90 mb-2">Highlights:</p>
                            <ul class="text-xs text-white/70 space-y-1">
                              {conference.highlights.map((highlight: string) => (
                                <li class="flex items-start">
                                  <span class="text-primary-400 mr-2">•</span>
                                  {highlight}
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}

                        {conference.materials && (
                          <a href={conference.materials} class="text-xs bg-secondary-600 hover:bg-secondary-700 text-white px-3 py-1 rounded-full transition-colors">
                            Access Materials
                          </a>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {educationData.learningResources.pastConferences?.length > 1 && (
                <div class="flex items-center justify-between mt-4">
                  <button class="carousel-prev bg-white/10 hover:bg-white/20 text-white p-2 rounded-full transition-colors" data-carousel="conferences">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                  </button>

                  <div class="flex space-x-1">
                    {educationData.learningResources.pastConferences?.map((_: any, index: number) => (
                      <button class={`carousel-dot w-2 h-2 rounded-full transition-colors ${index === 0 ? 'bg-white' : 'bg-white/40'}`} data-carousel="conferences" data-index={index}></button>
                    ))}
                  </div>

                  <button class="carousel-next bg-white/10 hover:bg-white/20 text-white p-2 rounded-full transition-colors" data-carousel="conferences">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                  </button>
                </div>
              )}
            </div>
          </div>

          <!-- Landmark Papers -->
          <div class="glass-card">
            <div class="flex items-center mb-6">
              <div class="w-12 h-12 bg-gradient-accent rounded-lg flex items-center justify-center mr-4">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                </svg>
              </div>
              <h3 class="text-2xl font-bold text-white">Landmark Papers</h3>
            </div>

            <div class="relative">
              <div class="carousel-container overflow-hidden" data-carousel="papers">
                <div class="carousel-track flex transition-transform duration-500 ease-in-out">
                  {educationData.learningResources.landmarkPapers?.map((paper: any) => (
                    <div class="carousel-slide flex-shrink-0 w-full">
                      <div class="bg-white/5 rounded-lg p-4 border border-white/10 mx-2">
                        <h4 class="font-semibold text-white mb-2 text-sm leading-tight">{paper.title}</h4>
                        <div class="text-xs text-white/70 mb-2">
                          <p class="mb-1">{paper.authors}</p>
                          <p><em>{paper.journal}</em> ({paper.year})</p>
                        </div>
                        <p class="text-xs text-white/70 mb-3 line-clamp-3">{paper.abstract}</p>
                        <div class="flex gap-2">
                          {paper.url && (
                            <a href={paper.url} target="_blank" rel="noopener noreferrer" class="text-xs bg-accent-600 hover:bg-accent-700 text-white px-3 py-1 rounded-full transition-colors">
                              Read Paper
                            </a>
                          )}
                          {paper.doi && (
                            <span class="text-xs text-white/50 px-2 py-1">
                              DOI: {paper.doi}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {educationData.learningResources.landmarkPapers?.length > 1 && (
                <div class="flex items-center justify-between mt-4">
                  <button class="carousel-prev bg-white/10 hover:bg-white/20 text-white p-2 rounded-full transition-colors" data-carousel="papers">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                  </button>

                  <div class="flex space-x-1">
                    {educationData.learningResources.landmarkPapers?.map((_: any, index: number) => (
                      <button class={`carousel-dot w-2 h-2 rounded-full transition-colors ${index === 0 ? 'bg-white' : 'bg-white/40'}`} data-carousel="papers" data-index={index}></button>
                    ))}
                  </div>

                  <button class="carousel-next bg-white/10 hover:bg-white/20 text-white p-2 rounded-full transition-colors" data-carousel="papers">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </section>
  )}
</Layout>

<script>
  // Carousel functionality for Learning Resources
  document.addEventListener('DOMContentLoaded', function() {
    const carousels = document.querySelectorAll('[data-carousel]');

    carousels.forEach(carousel => {
      const carouselName = carousel.getAttribute('data-carousel');
      const track = carousel.querySelector('.carousel-track');
      const slides = carousel.querySelectorAll('.carousel-slide');
      const prevBtn = document.querySelector(`.carousel-prev[data-carousel="${carouselName}"]`);
      const nextBtn = document.querySelector(`.carousel-next[data-carousel="${carouselName}"]`);
      const dots = document.querySelectorAll(`.carousel-dot[data-carousel="${carouselName}"]`);

      let currentIndex = 0;

      function updateCarousel() {
        if (track) {
          (track as HTMLElement).style.transform = `translateX(-${currentIndex * 100}%)`;
        }

        // Update dots
        dots.forEach((dot, index) => {
          if (index === currentIndex) {
            dot.classList.remove('bg-white/40');
            dot.classList.add('bg-white');
          } else {
            dot.classList.remove('bg-white');
            dot.classList.add('bg-white/40');
          }
        });
      }

      // Previous button
      if (prevBtn) {
        prevBtn.addEventListener('click', () => {
          currentIndex = currentIndex > 0 ? currentIndex - 1 : slides.length - 1;
          updateCarousel();
        });
      }

      // Next button
      if (nextBtn) {
        nextBtn.addEventListener('click', () => {
          currentIndex = currentIndex < slides.length - 1 ? currentIndex + 1 : 0;
          updateCarousel();
        });
      }

      // Dot navigation
      dots.forEach((dot, index) => {
        dot.addEventListener('click', () => {
          currentIndex = index;
          updateCarousel();
        });
      });

      // Auto-play (optional)
      setInterval(() => {
        if (slides.length > 1) {
          currentIndex = currentIndex < slides.length - 1 ? currentIndex + 1 : 0;
          updateCarousel();
        }
      }, 8000); // Change slide every 8 seconds
    });
  });
</script>
