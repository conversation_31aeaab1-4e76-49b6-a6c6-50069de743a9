---
import Layout from '../components/Layout.astro';
import <PERSON> from '../components/Hero.astro';
import { readFileSync } from 'fs';
import { join } from 'path';

// Read content data
const contentPath = join(process.cwd(), 'data', 'content.json');
const contentData = JSON.parse(readFileSync(contentPath, 'utf-8'));
const { site } = contentData;
---

<Layout title={site.title} description={site.description}>
  <Hero />
</Layout>
