/**
 * API utility functions for P2 Website
 * Handles communication with PHP backend APIs
 */

// Base API URL - update this for your Hostinger domain
const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? 'https://your-domain.com/api'  // Update with your actual domain
  : 'http://localhost/p2website/api';

// Generic API call function
async function apiCall(endpoint, options = {}) {
  const url = `${API_BASE_URL}/${endpoint}`;

  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include', // Include cookies for authentication
  };

  const finalOptions = { ...defaultOptions, ...options };

  try {
    const response = await fetch(url, finalOptions);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`API call failed for ${endpoint}:`, error);
    throw error;
  }
}

// Content API calls
export const contentApi = {
  get: () => apiCall('content.php'),
  update: (data) => apiCall('content.php', {
    method: 'PUT',
    body: JSON.stringify(data)
  })
};

// Members API calls
export const membersApi = {
  get: () => apiCall('members.php'),
  update: (data) => apiCall('members.php', {
    method: 'PUT',
    body: JSON.stringify(data)
  }),
  add: (member) => apiCall('members.php', {
    method: 'POST',
    body: JSON.stringify(member)
  }),
  delete: (id) => apiCall(`members.php?id=${id}`, {
    method: 'DELETE'
  })
};

// Education API calls
export const educationApi = {
  get: () => apiCall('education.php'),
  update: (data) => apiCall('education.php', {
    method: 'PUT',
    body: JSON.stringify(data)
  })
};

// Research API calls
export const researchApi = {
  get: () => apiCall('research.php'),
  update: (data) => apiCall('research.php', {
    method: 'PUT',
    body: JSON.stringify(data)
  })
};

// Projects API calls
export const projectsApi = {
  get: () => apiCall('projects.php'),
  update: (data) => apiCall('projects.php', {
    method: 'PUT',
    body: JSON.stringify(data)
  })
};

// Gallery API calls
export const galleryApi = {
  get: () => apiCall('gallery.php'),
  update: (data) => apiCall('gallery.php', {
    method: 'PUT',
    body: JSON.stringify(data)
  })
};

// Authentication API calls
export const authApi = {
  login: (credentials) => apiCall('auth.php', {
    method: 'POST',
    body: JSON.stringify(credentials)
  })
};

// Helper function to check if user is authenticated
export function isAuthenticated() {
  if (typeof document !== 'undefined') {
    return document.cookie.includes('auth-token=admin-authenticated');
  }
  return false;
}

// Helper function to logout
export function logout() {
  if (typeof document !== 'undefined') {
    document.cookie = 'auth-token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    window.location.href = '/admin';
  }
}

// Data fetching functions for static generation
export async function fetchContentData() {
  try {
    return await contentApi.get();
  } catch (error) {
    console.error('Failed to fetch content data:', error);
    // Return fallback data or empty structure
    return {
      site: {
        title: 'Pediatric POCUS Society',
        subtitle: 'Advancing Point-of-Care Ultrasound in Pediatric Medicine',
        description: 'Leading the future of pediatric point-of-care ultrasound through education, research, and global health initiatives.',
        hero: {
          title: 'Transforming Pediatric Care Through POCUS',
          subtitle: 'Empowering healthcare providers with cutting-edge ultrasound technology and training',
          highlights: []
        },
        upcomingEvents: []
      },
      navigation: [],
      adminResources: { links: [] }
    };
  }
}

export async function fetchMembersData() {
  try {
    return await membersApi.get();
  } catch (error) {
    console.error('Failed to fetch members data:', error);
    return { boardMembers: [] };
  }
}

export async function fetchEducationData() {
  try {
    return await educationApi.get();
  } catch (error) {
    console.error('Failed to fetch education data:', error);
    return {
      fellowshipPrograms: [],
      trainingResources: [],
      certificationInfo: { levels: [] },
      learningResources: {
        pastWebinars: [],
        pastConferences: [],
        landmarkPapers: []
      }
    };
  }
}

export async function fetchResearchData() {
  try {
    return await researchApi.get();
  } catch (error) {
    console.error('Failed to fetch research data:', error);
    return {
      currentProjects: [],
      howToGetInvolved: { opportunities: [] }
    };
  }
}

export async function fetchProjectsData() {
  try {
    return await projectsApi.get();
  } catch (error) {
    console.error('Failed to fetch projects data:', error);
    return { globalHealthProjects: [] };
  }
}

export async function fetchGalleryData() {
  try {
    return await galleryApi.get();
  } catch (error) {
    console.error('Failed to fetch gallery data:', error);
    return {
      title: 'P2 Through the Years',
      description: 'A visual journey through our organization\'s milestones and memorable moments',
      sections: []
    };
  }
}