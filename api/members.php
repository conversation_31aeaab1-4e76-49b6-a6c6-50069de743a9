<?php
/**
 * Members API for P2 Website
 * Handles board members data
 */

require_once '../database/config.php';

// Set JSON response headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, PUT, POST, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Cookie');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Authentication check for write operations
function isAuthenticated() {
    session_start();
    return isset($_SESSION['admin_authenticated']) && $_SESSION['admin_authenticated'] === true;
}

try {
    $pdo = getDbConnection();

    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Get all board members
        $members = getMultipleRecords($pdo, "
            SELECT * FROM board_members
            WHERE is_active = 1
            ORDER BY sort_order, id
        ");

        // Format response to match original JSON structure
        $response = [
            'boardMembers' => array_map(function($member) {
                return [
                    'id' => (int)$member['id'],
                    'name' => $member['name'],
                    'position' => $member['position'],
                    'specialty' => $member['specialty'],
                    'institution' => $member['institution'],
                    'bio' => $member['bio'],
                    'email' => $member['email'],
                    'image' => $member['image'],
                    'linkedin' => $member['linkedin']
                ];
            }, $members)
        ];

        echo json_encode($response);

    } elseif ($_SERVER['REQUEST_METHOD'] === 'PUT') {
        if (!isAuthenticated()) {
            http_response_code(401);
            echo json_encode(['success' => false, 'message' => 'Unauthorized']);
            exit();
        }

        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input || !isset($input['boardMembers'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid JSON data']);
            exit();
        }

        // Clear existing members
        executeQuery($pdo, "DELETE FROM board_members");

        // Insert new members
        foreach ($input['boardMembers'] as $index => $member) {
            insertRecord($pdo, "
                INSERT INTO board_members (id, name, position, specialty, institution, bio, email, image, linkedin, sort_order)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ", [
                $member['id'] ?? null,
                $member['name'],
                $member['position'],
                $member['specialty'] ?? null,
                $member['institution'] ?? null,
                $member['bio'] ?? null,
                $member['email'] ?? null,
                $member['image'] ?? null,
                $member['linkedin'] ?? null,
                $index
            ]);
        }

        echo json_encode(['success' => true, 'message' => 'Board members updated successfully']);

    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (!isAuthenticated()) {
            http_response_code(401);
            echo json_encode(['success' => false, 'message' => 'Unauthorized']);
            exit();
        }

        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid JSON data']);
            exit();
        }

        // Add new member
        $id = insertRecord($pdo, "
            INSERT INTO board_members (name, position, specialty, institution, bio, email, image, linkedin, sort_order)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ", [
            $input['name'],
            $input['position'],
            $input['specialty'] ?? null,
            $input['institution'] ?? null,
            $input['bio'] ?? null,
            $input['email'] ?? null,
            $input['image'] ?? null,
            $input['linkedin'] ?? null,
            $input['sort_order'] ?? 0
        ]);

        echo json_encode(['success' => true, 'message' => 'Board member added successfully', 'id' => $id]);

    } elseif ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
        if (!isAuthenticated()) {
            http_response_code(401);
            echo json_encode(['success' => false, 'message' => 'Unauthorized']);
            exit();
        }

        $id = $_GET['id'] ?? null;

        if (!$id) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Member ID required']);
            exit();
        }

        $affected = updateRecord($pdo, "UPDATE board_members SET is_active = 0 WHERE id = ?", [$id]);

        if ($affected > 0) {
            echo json_encode(['success' => true, 'message' => 'Board member deleted successfully']);
        } else {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Board member not found']);
        }

    } else {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }

} catch (Exception $e) {
    error_log("Members API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error']);
}
?>