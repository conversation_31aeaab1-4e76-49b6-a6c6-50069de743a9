<?php
/**
 * Research API for P2 Website
 * Handles research projects and opportunities
 */

require_once '../database/config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, PUT, POST, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Cookie');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

function isAuthenticated() {
    session_start();
    return isset($_SESSION['admin_authenticated']) && $_SESSION['admin_authenticated'] === true;
}

try {
    $pdo = getDbConnection();

    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $projects = getMultipleRecords($pdo, "
            SELECT * FROM research_projects
            WHERE is_active = 1
            ORDER BY id
        ");

        $opportunities = getMultipleRecords($pdo, "
            SELECT * FROM research_opportunities
            WHERE is_active = 1
            ORDER BY sort_order
        ");

        $response = [
            'currentProjects' => array_map(function($project) {
                return [
                    'id' => (int)$project['id'],
                    'title' => $project['title'],
                    'principalInvestigator' => $project['principal_investigator'],
                    'status' => $project['status'],
                    'startDate' => $project['start_date'],
                    'endDate' => $project['end_date'],
                    'funding' => $project['funding'],
                    'fundingSource' => $project['funding_source'],
                    'description' => $project['description'],
                    'objectives' => json_decode($project['objectives'] ?? '[]', true),
                    'currentPhase' => $project['current_phase'],
                    'participants' => (int)$project['participants'],
                    'publications' => (int)$project['publications']
                ];
            }, $projects),
            'howToGetInvolved' => [
                'title' => 'Join Our Research Community',
                'description' => 'We welcome collaboration from healthcare providers, researchers, and institutions worldwide.',
                'opportunities' => array_map(function($opp) {
                    return [
                        'type' => $opp['type'],
                        'description' => $opp['description'],
                        'requirements' => json_decode($opp['requirements'] ?? '[]', true),
                        'contact' => $opp['contact']
                    ];
                }, $opportunities)
            ]
        ];

        echo json_encode($response);

    } elseif ($_SERVER['REQUEST_METHOD'] === 'PUT') {
        if (!isAuthenticated()) {
            http_response_code(401);
            echo json_encode(['success' => false, 'message' => 'Unauthorized']);
            exit();
        }

        $input = json_decode(file_get_contents('php://input'), true);

        if (isset($input['currentProjects'])) {
            executeQuery($pdo, "DELETE FROM research_projects");

            foreach ($input['currentProjects'] as $project) {
                insertRecord($pdo, "
                    INSERT INTO research_projects (id, title, principal_investigator, status, start_date, end_date,
                                                 funding, funding_source, description, objectives, current_phase,
                                                 participants, publications)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ", [
                    $project['id'],
                    $project['title'],
                    $project['principalInvestigator'] ?? null,
                    $project['status'] ?? null,
                    $project['startDate'] ?? null,
                    $project['endDate'] ?? null,
                    $project['funding'] ?? null,
                    $project['fundingSource'] ?? null,
                    $project['description'] ?? null,
                    json_encode($project['objectives'] ?? []),
                    $project['currentPhase'] ?? null,
                    $project['participants'] ?? 0,
                    $project['publications'] ?? 0
                ]);
            }
        }

        echo json_encode(['success' => true, 'message' => 'Research data updated successfully']);

    } else {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }

} catch (Exception $e) {
    error_log("Research API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error']);
}
?>