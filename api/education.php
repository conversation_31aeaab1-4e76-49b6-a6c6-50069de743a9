<?php
/**
 * Education API for P2 Website
 * Handles fellowship programs, training resources, certification, and learning resources
 */

require_once '../database/config.php';

// Set JSON response headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, PUT, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Cookie');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Authentication check for PUT requests
function isAuthenticated() {
    session_start();
    return isset($_SESSION['admin_authenticated']) && $_SESSION['admin_authenticated'] === true;
}

try {
    $pdo = getDbConnection();

    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Get all education data
        $fellowshipPrograms = getMultipleRecords($pdo, "
            SELECT * FROM fellowship_programs
            WHERE is_active = 1
            ORDER BY id
        ");

        $trainingResources = getMultipleRecords($pdo, "
            SELECT * FROM training_resources
            WHERE is_active = 1
            ORDER BY id
        ");

        $certificationLevels = getMultipleRecords($pdo, "
            SELECT * FROM certification_levels
            WHERE is_active = 1
            ORDER BY sort_order
        ");

        $webinars = getMultipleRecords($pdo, "
            SELECT * FROM webinars
            WHERE is_active = 1
            ORDER BY date DESC
        ");

        $conferences = getMultipleRecords($pdo, "
            SELECT * FROM conferences
            WHERE is_active = 1
            ORDER BY date DESC
        ");

        $papers = getMultipleRecords($pdo, "
            SELECT * FROM landmark_papers
            WHERE is_active = 1
            ORDER BY year DESC, id
        ");

        // Format response to match original JSON structure
        $response = [
            'fellowshipPrograms' => array_map(function($program) {
                return [
                    'id' => (int)$program['id'],
                    'title' => $program['title'],
                    'duration' => $program['duration'],
                    'location' => $program['location'],
                    'description' => $program['description'],
                    'curriculum' => json_decode($program['curriculum'] ?? '[]', true),
                    'requirements' => json_decode($program['requirements'] ?? '[]', true),
                    'applicationDeadline' => $program['application_deadline'],
                    'startDate' => $program['start_date'],
                    'positions' => (int)$program['positions'],
                    'stipend' => $program['stipend']
                ];
            }, $fellowshipPrograms),

            'trainingResources' => array_map(function($resource) {
                return [
                    'title' => $resource['title'],
                    'type' => $resource['type'],
                    'duration' => $resource['duration'],
                    'cost' => $resource['cost'],
                    'description' => $resource['description'],
                    'modules' => json_decode($resource['modules'] ?? '[]', true),
                    'locations' => json_decode($resource['locations'] ?? '[]', true),
                    'features' => json_decode($resource['features'] ?? '[]', true)
                ];
            }, $trainingResources),

            'certificationInfo' => [
                'title' => 'Pediatric POCUS Certification Program',
                'description' => 'Comprehensive certification program ensuring competency in pediatric point-of-care ultrasound',
                'levels' => array_map(function($level) {
                    return [
                        'name' => $level['name'],
                        'requirements' => json_decode($level['requirements'] ?? '[]', true),
                        'validityPeriod' => $level['validity_period'],
                        'cost' => $level['cost']
                    ];
                }, $certificationLevels)
            ],

            'learningResources' => [
                'title' => 'Learning Resources',
                'description' => 'Access our comprehensive library of educational materials and past events',
                'pastWebinars' => array_map(function($webinar) {
                    return [
                        'id' => (int)$webinar['id'],
                        'title' => $webinar['title'],
                        'date' => $webinar['date'],
                        'duration' => $webinar['duration'],
                        'presenter' => $webinar['presenter'],
                        'description' => $webinar['description'],
                        'videoUrl' => $webinar['video_url'],
                        'slides' => $webinar['slides_url']
                    ];
                }, $webinars),
                'pastConferences' => array_map(function($conference) {
                    return [
                        'id' => (int)$conference['id'],
                        'title' => $conference['title'],
                        'date' => $conference['date'],
                        'location' => $conference['location'],
                        'description' => $conference['description'],
                        'highlights' => json_decode($conference['highlights'] ?? '[]', true),
                        'materials' => $conference['materials_url']
                    ];
                }, $conferences),
                'landmarkPapers' => array_map(function($paper) {
                    return [
                        'id' => (int)$paper['id'],
                        'title' => $paper['title'],
                        'authors' => $paper['authors'],
                        'journal' => $paper['journal'],
                        'year' => (int)$paper['year'],
                        'doi' => $paper['doi'],
                        'abstract' => $paper['abstract'],
                        'url' => $paper['url']
                    ];
                }, $papers)
            ]
        ];

        echo json_encode($response);

    } elseif ($_SERVER['REQUEST_METHOD'] === 'PUT') {
        if (!isAuthenticated()) {
            http_response_code(401);
            echo json_encode(['success' => false, 'message' => 'Unauthorized']);
            exit();
        }

        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid JSON data']);
            exit();
        }

        // Update fellowship programs
        if (isset($input['fellowshipPrograms'])) {
            executeQuery($pdo, "DELETE FROM fellowship_programs");

            foreach ($input['fellowshipPrograms'] as $program) {
                insertRecord($pdo, "
                    INSERT INTO fellowship_programs (id, title, duration, location, description, curriculum,
                                                   requirements, application_deadline, start_date, positions, stipend)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ", [
                    $program['id'],
                    $program['title'],
                    $program['duration'] ?? null,
                    $program['location'] ?? null,
                    $program['description'] ?? null,
                    json_encode($program['curriculum'] ?? []),
                    json_encode($program['requirements'] ?? []),
                    $program['applicationDeadline'] ?? null,
                    $program['startDate'] ?? null,
                    $program['positions'] ?? null,
                    $program['stipend'] ?? null
                ]);
            }
        }

        // Update other sections as needed...
        echo json_encode(['success' => true, 'message' => 'Education data updated successfully']);

    } else {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }

} catch (Exception $e) {
    error_log("Education API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error']);
}
?>