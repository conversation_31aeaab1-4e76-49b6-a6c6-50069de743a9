<?php
/**
 * Content API for P2 Website
 * Handles site content, navigation, and admin resources
 */

require_once '../database/config.php';

// Set JSON response headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, PUT, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Cookie');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Authentication check for PUT requests
function isAuthenticated() {
    session_start();
    return isset($_SESSION['admin_authenticated']) && $_SESSION['admin_authenticated'] === true;
}

try {
    $pdo = getDbConnection();

    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // Get all content data
        $siteContent = getSingleRecord($pdo, "SELECT * FROM site_content ORDER BY id DESC LIMIT 1");
        $navigation = getMultipleRecords($pdo, "SELECT * FROM navigation WHERE is_active = 1 ORDER BY sort_order");
        $adminResources = getMultipleRecords($pdo, "SELECT * FROM admin_resources WHERE is_active = 1 ORDER BY sort_order");
        $events = getMultipleRecords($pdo, "SELECT * FROM events WHERE is_active = 1 ORDER BY date");

        // Format the response to match the original JSON structure
        $response = [
            'site' => [
                'title' => $siteContent['title'] ?? '',
                'subtitle' => $siteContent['subtitle'] ?? '',
                'description' => $siteContent['description'] ?? '',
                'hero' => [
                    'title' => $siteContent['hero_title'] ?? '',
                    'subtitle' => $siteContent['hero_subtitle'] ?? '',
                    'highlights' => json_decode($siteContent['hero_highlights'] ?? '[]', true)
                ],
                'upcomingEvents' => array_map(function($event) {
                    return [
                        'id' => (int)$event['id'],
                        'title' => $event['title'],
                        'date' => $event['date'],
                        'time' => $event['time'],
                        'location' => $event['location'],
                        'description' => $event['description'],
                        'registrationLink' => $event['registration_link']
                    ];
                }, $events)
            ],
            'navigation' => array_map(function($nav) {
                return [
                    'name' => $nav['name'],
                    'href' => $nav['href']
                ];
            }, $navigation),
            'adminResources' => [
                'title' => 'Administrative Resources',
                'description' => 'Access important policies, research papers, and administrative statements',
                'links' => array_map(function($resource) {
                    return [
                        'title' => $resource['title'],
                        'description' => $resource['description'],
                        'url' => $resource['url'],
                        'category' => $resource['category']
                    ];
                }, $adminResources)
            ]
        ];

        echo json_encode($response);

    } elseif ($_SERVER['REQUEST_METHOD'] === 'PUT') {
        if (!isAuthenticated()) {
            http_response_code(401);
            echo json_encode(['success' => false, 'message' => 'Unauthorized']);
            exit();
        }

        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid JSON data']);
            exit();
        }

        // Update site content
        if (isset($input['site'])) {
            $site = $input['site'];

            // Check if site content exists
            $existingContent = getSingleRecord($pdo, "SELECT id FROM site_content LIMIT 1");

            if ($existingContent) {
                // Update existing content
                updateRecord($pdo, "
                    UPDATE site_content SET
                        title = ?, subtitle = ?, description = ?,
                        hero_title = ?, hero_subtitle = ?, hero_highlights = ?,
                        updated_at = NOW()
                    WHERE id = ?
                ", [
                    $site['title'],
                    $site['subtitle'],
                    $site['description'],
                    $site['hero']['title'],
                    $site['hero']['subtitle'],
                    json_encode($site['hero']['highlights']),
                    $existingContent['id']
                ]);
            } else {
                // Insert new content
                insertRecord($pdo, "
                    INSERT INTO site_content (title, subtitle, description, hero_title, hero_subtitle, hero_highlights)
                    VALUES (?, ?, ?, ?, ?, ?)
                ", [
                    $site['title'],
                    $site['subtitle'],
                    $site['description'],
                    $site['hero']['title'],
                    $site['hero']['subtitle'],
                    json_encode($site['hero']['highlights'])
                ]);
            }

            // Update events
            if (isset($site['upcomingEvents'])) {
                // Clear existing events
                executeQuery($pdo, "DELETE FROM events");

                // Insert new events
                foreach ($site['upcomingEvents'] as $event) {
                    insertRecord($pdo, "
                        INSERT INTO events (title, date, time, location, description, registration_link)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ", [
                        $event['title'],
                        $event['date'],
                        $event['time'] ?? null,
                        $event['location'] ?? null,
                        $event['description'] ?? null,
                        $event['registrationLink'] ?? null
                    ]);
                }
            }
        }

        // Update navigation
        if (isset($input['navigation'])) {
            // Clear existing navigation
            executeQuery($pdo, "DELETE FROM navigation");

            // Insert new navigation
            foreach ($input['navigation'] as $index => $nav) {
                insertRecord($pdo, "
                    INSERT INTO navigation (name, href, sort_order)
                    VALUES (?, ?, ?)
                ", [
                    $nav['name'],
                    $nav['href'],
                    $index
                ]);
            }
        }

        // Update admin resources
        if (isset($input['adminResources']['links'])) {
            // Clear existing admin resources
            executeQuery($pdo, "DELETE FROM admin_resources");

            // Insert new admin resources
            foreach ($input['adminResources']['links'] as $index => $resource) {
                insertRecord($pdo, "
                    INSERT INTO admin_resources (title, description, url, category, sort_order)
                    VALUES (?, ?, ?, ?, ?)
                ", [
                    $resource['title'],
                    $resource['description'] ?? null,
                    $resource['url'] ?? null,
                    $resource['category'] ?? null,
                    $index
                ]);
            }
        }

        echo json_encode(['success' => true, 'message' => 'Content updated successfully']);

    } else {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }

} catch (Exception $e) {
    error_log("Content API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error']);
}
?>