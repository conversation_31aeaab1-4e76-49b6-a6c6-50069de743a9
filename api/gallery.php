<?php
/**
 * Gallery API for P2 Website
 * Handles photo gallery sections and photos
 */

require_once '../database/config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, PUT, POST, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Cookie');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

function isAuthenticated() {
    session_start();
    return isset($_SESSION['admin_authenticated']) && $_SESSION['admin_authenticated'] === true;
}

try {
    $pdo = getDbConnection();

    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $sections = getMultipleRecords($pdo, "
            SELECT * FROM gallery_sections
            WHERE is_active = 1
            ORDER BY sort_order, id
        ");

        $response = [
            'title' => 'P2 Through the Years',
            'description' => 'A visual journey through our organization\'s milestones and memorable moments',
            'sections' => []
        ];

        foreach ($sections as $section) {
            $photos = getMultipleRecords($pdo, "
                SELECT * FROM gallery_photos
                WHERE section_id = ? AND is_active = 1
                ORDER BY sort_order, id
            ", [$section['id']]);

            $response['sections'][] = [
                'id' => (int)$section['id'],
                'title' => $section['title'],
                'year' => $section['year'],
                'description' => $section['description'],
                'photos' => array_map(function($photo) {
                    return [
                        'url' => $photo['url'],
                        'caption' => $photo['caption'],
                        'alt' => $photo['alt_text']
                    ];
                }, $photos)
            ];
        }

        echo json_encode($response);

    } elseif ($_SERVER['REQUEST_METHOD'] === 'PUT') {
        if (!isAuthenticated()) {
            http_response_code(401);
            echo json_encode(['success' => false, 'message' => 'Unauthorized']);
            exit();
        }

        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input || !isset($input['sections'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid JSON data']);
            exit();
        }

        // Clear existing gallery data
        executeQuery($pdo, "DELETE FROM gallery_photos");
        executeQuery($pdo, "DELETE FROM gallery_sections");

        // Insert new sections and photos
        foreach ($input['sections'] as $index => $section) {
            $sectionId = insertRecord($pdo, "
                INSERT INTO gallery_sections (id, title, year, description, sort_order)
                VALUES (?, ?, ?, ?, ?)
            ", [
                $section['id'],
                $section['title'],
                $section['year'] ?? null,
                $section['description'] ?? null,
                $index
            ]);

            if (isset($section['photos'])) {
                foreach ($section['photos'] as $photoIndex => $photo) {
                    insertRecord($pdo, "
                        INSERT INTO gallery_photos (section_id, url, caption, alt_text, sort_order)
                        VALUES (?, ?, ?, ?, ?)
                    ", [
                        $section['id'],
                        $photo['url'],
                        $photo['caption'] ?? null,
                        $photo['alt'] ?? null,
                        $photoIndex
                    ]);
                }
            }
        }

        echo json_encode(['success' => true, 'message' => 'Gallery updated successfully']);

    } else {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }

} catch (Exception $e) {
    error_log("Gallery API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error']);
}
?>