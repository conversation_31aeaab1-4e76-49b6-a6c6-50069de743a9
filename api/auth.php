<?php
/**
 * Authentication API for P2 Website Admin Panel
 */

require_once '../database/config.php';

// Set JSON response headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

try {
    $pdo = getDbConnection();
    $input = json_decode(file_get_contents('php://input'), true);

    if (!isset($input['username']) || !isset($input['password'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Username and password required']);
        exit();
    }

    $username = $input['username'];
    $password = $input['password'];

    // Get user from database
    $user = getSingleRecord($pdo,
        "SELECT * FROM admin_users WHERE username = ? AND is_active = 1",
        [$username]
    );

    if (!$user || !password_verify($password, $user['password_hash'])) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Invalid credentials']);
        exit();
    }

    // Update last login
    updateRecord($pdo,
        "UPDATE admin_users SET last_login = NOW() WHERE id = ?",
        [$user['id']]
    );

    // Start session and set authentication
    session_start();
    $_SESSION['admin_authenticated'] = true;
    $_SESSION['admin_user_id'] = $user['id'];
    $_SESSION['admin_username'] = $user['username'];

    // Set cookie for client-side authentication check
    setcookie('auth-token', 'admin-authenticated', time() + (24 * 60 * 60), '/');

    echo json_encode([
        'success' => true,
        'message' => 'Authentication successful',
        'user' => [
            'id' => $user['id'],
            'username' => $user['username'],
            'email' => $user['email']
        ]
    ]);

} catch (Exception $e) {
    error_log("Auth error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Authentication failed']);
}
?>