<?php
/**
 * Projects API for P2 Website
 * Handles global health projects
 */

require_once '../database/config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, PUT, POST, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Cookie');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

function isAuthenticated() {
    session_start();
    return isset($_SESSION['admin_authenticated']) && $_SESSION['admin_authenticated'] === true;
}

try {
    $pdo = getDbConnection();

    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $projects = getMultipleRecords($pdo, "
            SELECT * FROM global_health_projects
            WHERE is_active = 1
            ORDER BY id
        ");

        $response = [
            'globalHealthProjects' => array_map(function($project) {
                return [
                    'id' => (int)$project['id'],
                    'title' => $project['title'],
                    'location' => $project['location'],
                    'status' => $project['status'],
                    'leadInvestigator' => $project['lead_investigator'],
                    'startDate' => $project['start_date'],
                    'endDate' => $project['end_date'],
                    'description' => $project['description'],
                    'participants' => (int)$project['participants'],
                    'budget' => $project['budget'],
                    'outcomes' => $project['outcomes']
                ];
            }, $projects)
        ];

        echo json_encode($response);

    } elseif ($_SERVER['REQUEST_METHOD'] === 'PUT') {
        if (!isAuthenticated()) {
            http_response_code(401);
            echo json_encode(['success' => false, 'message' => 'Unauthorized']);
            exit();
        }

        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input || !isset($input['globalHealthProjects'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid JSON data']);
            exit();
        }

        // Clear existing projects
        executeQuery($pdo, "DELETE FROM global_health_projects");

        // Insert new projects
        foreach ($input['globalHealthProjects'] as $project) {
            insertRecord($pdo, "
                INSERT INTO global_health_projects (id, title, location, status, lead_investigator, start_date,
                                                  end_date, description, participants, budget, outcomes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ", [
                $project['id'],
                $project['title'],
                $project['location'] ?? null,
                $project['status'] ?? null,
                $project['leadInvestigator'] ?? null,
                $project['startDate'] ?? null,
                $project['endDate'] ?? null,
                $project['description'] ?? null,
                $project['participants'] ?? 0,
                $project['budget'] ?? null,
                $project['outcomes'] ?? null
            ]);
        }

        echo json_encode(['success' => true, 'message' => 'Global health projects updated successfully']);

    } else {
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    }

} catch (Exception $e) {
    error_log("Projects API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error']);
}
?>