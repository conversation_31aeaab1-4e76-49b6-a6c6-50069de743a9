<?php
/**
 * PHP Syntax Test Script
 * Tests all PHP files for syntax errors
 */

echo "Testing PHP files for syntax errors...\n\n";

$phpFiles = [
    'database/config.php',
    'database/migrate_data.php',
    'api/auth.php',
    'api/content.php',
    'api/members.php',
    'api/education.php',
    'api/research.php',
    'api/gallery.php',
    'api/projects.php'
];

$errors = [];

foreach ($phpFiles as $file) {
    if (!file_exists($file)) {
        $errors[] = "File not found: $file";
        continue;
    }

    // Check syntax
    $output = [];
    $returnCode = 0;
    exec("php -l $file 2>&1", $output, $returnCode);

    if ($returnCode !== 0) {
        $errors[] = "Syntax error in $file: " . implode("\n", $output);
    } else {
        echo "✓ $file - OK\n";
    }
}

if (empty($errors)) {
    echo "\n✅ All PHP files passed syntax check!\n";
} else {
    echo "\n❌ Errors found:\n";
    foreach ($errors as $error) {
        echo "- $error\n";
    }
}

// Test database configuration (without connecting)
echo "\nTesting database configuration...\n";
if (file_exists('database/config.php')) {
    include_once 'database/config.php';

    if (isset($dsn) && isset($username) && isset($password)) {
        echo "✓ Database configuration variables are set\n";

        if ($username === 'your_db_username' || $password === 'your_db_password') {
            echo "⚠️  Warning: Please update database credentials in database/config.php\n";
        }
    } else {
        echo "❌ Database configuration variables missing\n";
    }
} else {
    echo "❌ Database configuration file not found\n";
}

echo "\nTest completed.\n";
?>